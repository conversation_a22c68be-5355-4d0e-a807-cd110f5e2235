// -*- coding: utf-8 -*-
//
// Copyright (C) 2020 The SymbiFlow Authors.
//
// Use of this source code is governed by a ISC-style
// license that can be found in the LICENSE file or at
// https://opensource.org/licenses/ISC
// SPDX-License-Identifier: ISC


  typedef bit      bitq_t  [$];
  typedef bit[7:0] byteq_t [$];

  `include "svtb_scoreboard_config.svh"
  `include "svtb_scoreboard.svh"

endpackage

`endif
