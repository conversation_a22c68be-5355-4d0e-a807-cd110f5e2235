// -*- coding: utf-8 -*-
//
// Copyright (C) 2020 The SymbiFlow Authors.
//
// Use of this source code is governed by a ISC-style
// license that can be found in the LICENSE file or at
// https://opensource.org/licenses/ISC
// SPDX-License-Identifier: ISC


package sys_uvc_pkg;
   `include "uvm_macros.svh"
   import uvm_pkg::*;

   `include "sys_trans.svh"
   `include "sys_sequencer.svh"
   `include "sys_config.svh"
   `include "sys_driver.svh"
   `include "sys_monitor.svh"
   `include "sys_agent.svh"

   `include "sys_init_seq.svh"

endpackage
