// -*- coding: utf-8 -*-
//
// Copyright (C) 2020 The SymbiFlow Authors.
//
// Use of this source code is governed by a ISC-style
// license that can be found in the LICENSE file or at
// https://opensource.org/licenses/ISC
// SPDX-License-Identifier: ISC

package axi_agent_pkg;
   `include "uvm_macros.svh"
   import uvm_pkg::*;

   `include "axi_typedefs.svh"
   `include "axi_trans.svh"
   `include "axi_sequencer.svh"
   `include "axis_memory.svh"
   `include "axi_agent_config.svh"
   `include "axi_driver.svh"
   `include "axi_monitor.svh"
   `include "axi_agent.svh"
endpackage
