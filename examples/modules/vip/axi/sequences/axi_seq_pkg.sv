// -*- coding: utf-8 -*-
//
// Copyright (C) 2020 The SymbiFlow Authors.
//
// Use of this source code is governed by a ISC-style
// license that can be found in the LICENSE file or at
// https://opensource.org/licenses/ISC
// SPDX-License-Identifier: ISC


package axi_seq_pkg;
   `include "uvm_macros.svh"
   import uvm_pkg::*;

   import sys_uvc_pkg::*;
   import axi_agent_pkg::*;
   import axi_env_pkg::*;

   `include "axi_seq_list.svh"
endpackage
