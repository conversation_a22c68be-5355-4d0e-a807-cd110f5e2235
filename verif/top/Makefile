#==============================================================================
# File: Makefile
# Description: Makefile for vcom_temp_comp verification
#==============================================================================

# Default simulator (can be overridden)
SIM ?= questa

# Test name (can be overridden)
TEST ?= vcom_temp_comp_base_test

# Seed for randomization
SEED ?= 1

# UVM verbosity level
VERBOSITY ?= UVM_MEDIUM

# Coverage options
COV ?= 1

# Wave dumping
WAVES ?= 0

# Directories
RTL_DIR = ../../rtl
VERIF_DIR = ..
TOP_DIR = .

# Common simulator options
COMMON_OPTS = +UVM_TESTNAME=$(TEST) \
              +UVM_VERBOSITY=$(VERBOSITY) \
              +ntb_random_seed=$(SEED) \
              -sv \
              -uvm

# Coverage options
ifeq ($(COV), 1)
    COV_OPTS = +cover=bcesf +cover=tgl
else
    COV_OPTS = 
endif

# Wave options
ifeq ($(WAVES), 1)
    WAVE_OPTS = +define+DUMP_WAVES
else
    WAVE_OPTS = 
endif

# Simulator specific settings
ifeq ($(SIM), questa)
    COMP = vlog
    SIM_CMD = vsim
    COMP_OPTS = -f compile.f $(COMMON_OPTS) $(COV_OPTS) $(WAVE_OPTS)
    SIM_OPTS = -c -do "run -all; quit -f" tb_top
    COV_MERGE = vcover merge
    COV_REPORT = vcover report
endif

ifeq ($(SIM), vcs)
    COMP = vcs
    SIM_CMD = ./simv
    COMP_OPTS = -f compile.f $(COMMON_OPTS) $(COV_OPTS) $(WAVE_OPTS) -o simv
    SIM_OPTS = 
    COV_MERGE = urg -dir
    COV_REPORT = urg -dir
endif

ifeq ($(SIM), xcelium)
    COMP = xrun
    SIM_CMD = 
    COMP_OPTS = -f compile.f $(COMMON_OPTS) $(COV_OPTS) $(WAVE_OPTS) -access +rwc
    SIM_OPTS = 
    COV_MERGE = imc -exec
    COV_REPORT = imc -exec
endif

# Default target
all: compile run

# Compile target
compile:
	@echo "Compiling with $(SIM)..."
	$(COMP) $(COMP_OPTS)

# Run simulation
run:
	@echo "Running test $(TEST) with seed $(SEED)..."
ifeq ($(SIM), questa)
	$(SIM_CMD) $(SIM_OPTS)
else ifeq ($(SIM), vcs)
	$(SIM_CMD) $(SIM_OPTS)
else ifeq ($(SIM), xcelium)
	# For xcelium, compile and run are combined
	@echo "Xcelium compile and run combined"
endif

# Clean build artifacts
clean:
	rm -rf work/
	rm -rf simv*
	rm -rf csrc/
	rm -rf *.log
	rm -rf *.vcd
	rm -rf *.wlf
	rm -rf transcript
	rm -rf *.ucdb
	rm -rf urgReport/
	rm -rf cov_work/
	rm -rf xcelium.d/
	rm -rf *.shm/
	rm -rf *.key
	rm -rf *.trn
	rm -rf *.dsn

# Regression tests
regression:
	@echo "Running regression tests..."
	$(MAKE) TEST=vcom_temp_comp_base_test SEED=1
	$(MAKE) TEST=vcom_temp_comp_config_test SEED=2
	$(MAKE) TEST=vcom_temp_comp_regression_test SEED=3

# Coverage merge and report
coverage:
ifeq ($(SIM), questa)
	$(COV_MERGE) coverage.ucdb *.ucdb
	$(COV_REPORT) coverage.ucdb -html -htmldir cov_html
else ifeq ($(SIM), vcs)
	$(COV_MERGE) simv.vdb -format both -report urgReport
else ifeq ($(SIM), xcelium)
	$(COV_MERGE) "merge -out merged.ucd *.ucd; report -html -out cov_html merged.ucd"
endif

# Debug with GUI
gui:
ifeq ($(SIM), questa)
	vsim -gui tb_top
else ifeq ($(SIM), vcs)
	$(SIM_CMD) -gui
else ifeq ($(SIM), xcelium)
	xrun -f compile.f $(COMMON_OPTS) -gui -access +rwc
endif

# Help target
help:
	@echo "Available targets:"
	@echo "  all        - Compile and run (default)"
	@echo "  compile    - Compile only"
	@echo "  run        - Run simulation only"
	@echo "  clean      - Clean build artifacts"
	@echo "  regression - Run regression tests"
	@echo "  coverage   - Generate coverage report"
	@echo "  gui        - Run with GUI"
	@echo "  help       - Show this help"
	@echo ""
	@echo "Variables:"
	@echo "  SIM        - Simulator (questa/vcs/xcelium) [$(SIM)]"
	@echo "  TEST       - Test name [$(TEST)]"
	@echo "  SEED       - Random seed [$(SEED)]"
	@echo "  VERBOSITY  - UVM verbosity [$(VERBOSITY)]"
	@echo "  COV        - Enable coverage (0/1) [$(COV)]"
	@echo "  WAVES      - Enable wave dump (0/1) [$(WAVES)]"
	@echo ""
	@echo "Examples:"
	@echo "  make TEST=vcom_temp_comp_config_test SEED=42"
	@echo "  make SIM=vcs COV=1 WAVES=1"
	@echo "  make regression"

.PHONY: all compile run clean regression coverage gui help
