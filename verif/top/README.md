# VCOM Temperature Compensation Verification Environment

## Overview

This is a UVM-based verification environment for the `vcom_temp_comp` module, which implements temperature compensation for VCOM and Gamma voltages in LCD display systems.

## Directory Structure

```
verif/
├── top/                    # Testbench top level
│   ├── tb_top.sv          # Testbench top module
│   ├── vcom_temp_comp_if.sv # Interface definition
│   ├── vcom_temp_comp_pkg.sv # Common package
│   ├── compile.f          # File list for compilation
│   ├── Makefile          # Build automation
│   └── README.md         # This file
├── env/                   # UVM environment
├── sequences/             # UVM sequences
├── tests/                 # UVM tests
└── vip_libs/             # Verification IP libraries
    ├── config_agent/     # I2C configuration agent
    ├── temp_agent/       # Temperature input agent
    └── vcom_temp_comp/   # DUT monitoring agents
```

## Key Features

### Clock Domain Handling
- **clk**: Main system clock (100MHz)
- **clk_rg**: I2C configuration clock (~50MHz, half speed of main clock)
- **CDC Monitoring**: Built-in clock domain crossing checks
- **Reset Sequencing**: rst_rg_n released early, rst_n released later

### Interface Features
- **Clocking Blocks**: Separate clocking blocks for different agents
- **Modports**: Clean separation of concerns for different agents
- **Assertions**: Protocol checking and timing verification
- **Coverage**: CDC scenario coverage

### Verification Components
- **Config Agent**: Handles I2C-like configuration protocol
- **Temp Agent**: Manages temperature input stimuli
- **Monitors**: Separate monitoring for VCOM and Gamma outputs
- **Scoreboard**: Reference model for compensation calculations

## Quick Start

### Prerequisites
- UVM library installed
- Simulator (Questa/VCS/Xcelium) available
- Environment variable UVM_HOME set

### Basic Usage

1. **Compile and run default test:**
   ```bash
   make
   ```

2. **Run specific test:**
   ```bash
   make TEST=vcom_temp_comp_config_test
   ```

3. **Run with coverage:**
   ```bash
   make COV=1
   ```

4. **Run with waves:**
   ```bash
   make WAVES=1
   ```

5. **Run regression:**
   ```bash
   make regression
   ```

### Advanced Usage

1. **Different simulator:**
   ```bash
   make SIM=vcs TEST=vcom_temp_comp_base_test
   ```

2. **Custom seed and verbosity:**
   ```bash
   make SEED=42 VERBOSITY=UVM_HIGH
   ```

3. **Debug with GUI:**
   ```bash
   make gui
   ```

4. **Generate coverage report:**
   ```bash
   make coverage
   ```

## Test Scenarios

### Configuration Tests
- I2C configuration protocol verification
- Parameter boundary testing
- Clock domain crossing validation
- para_wr signal timing checks

### Temperature Compensation Tests
- Linear compensation algorithm verification
- Temperature sweep testing
- Coefficient boundary testing
- Enable/disable functionality

### Trigger Mechanism Tests
- Temperature change trigger
- Timer-based trigger
- Threshold testing
- Mode switching

### Precision Tests
- VCOM integer precision verification
- Gamma high-precision (s,9,5) verification
- Saturation behavior testing
- Rounding accuracy checks

## Key Signals and Timing

### Configuration Signals (clk_rg domain)
- `para_wr`: Configuration completion pulse
- `vcom_tcomp_coeff`: VCOM compensation coefficient (u,1,7)
- `gamma_tcomp_coeff`: Gamma compensation coefficient (u,1,7)
- `vcom`: Base VCOM value
- `temp_vcom`: Reference temperature

### Runtime Signals (clk domain)
- `temp`: Current temperature
- `temp_vld`: Temperature valid signal
- `timer_1s_vld`: 1-second timer pulse

### Output Signals
- `vcom_tcomp`: Final compensated VCOM value
- `gamma_tcomp`: Gamma compensation increment (s,9,5)
- `gamma_tcomp_vld`: Gamma result valid

## Compensation Algorithm

The module implements linear compensation:

**VCOM**: `Y_vcom = K_vcom * (Temp - T_ref) + B_vcom`
**Gamma**: `Y_gamma = K_gamma * (Temp - T_ref)`

Where:
- K: Compensation coefficient (u,1,7 format)
- Temp: Current temperature
- T_ref: Reference temperature
- B_vcom: Base VCOM value

## Debugging Tips

1. **Enable debug messages:**
   ```bash
   make VERBOSITY=UVM_DEBUG
   ```

2. **Check CDC events:**
   Look for "CDC Event" and "Config Event" messages in logs

3. **Monitor configuration frequency:**
   Check "Config frequency" messages for timing analysis

4. **Use assertions:**
   Built-in assertions will catch protocol violations

5. **Coverage analysis:**
   Use coverage reports to identify untested scenarios

## Troubleshooting

### Common Issues

1. **Compilation errors:**
   - Check UVM_HOME environment variable
   - Verify file paths in compile.f
   - Ensure all packages are properly imported

2. **Simulation hangs:**
   - Check clock generation
   - Verify reset sequence
   - Look for infinite loops in sequences

3. **Protocol violations:**
   - Check para_wr timing
   - Verify clock domain assignments
   - Review configuration sequence timing

4. **Coverage gaps:**
   - Add more randomization constraints
   - Extend test scenarios
   - Check corner cases

## Contact

For questions or issues with this verification environment, please refer to the project documentation or contact the verification team.
