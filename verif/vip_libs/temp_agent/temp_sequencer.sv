//==============================================================================
// File: temp_sequencer.sv
// Description: Temperature sequencer for runtime temperature stimuli
//==============================================================================

class temp_sequencer extends uvm_sequencer #(temp_transaction);
    
    `uvm_component_utils(temp_sequencer)
    
    // Configuration parameters
    int max_outstanding_reqs = 5;  // Allow multiple temp requests
    bit enable_flow_control = 0;   // Usually no flow control needed for temp
    
    // Statistics
    int total_requests = 0;
    int completed_requests = 0;
    int temp_update_requests = 0;
    int sweep_requests = 0;
    int timer_requests = 0;
    int ramp_requests = 0;
    int spike_requests = 0;
    
    // Constructor
    function new(string name = "temp_sequencer", uvm_component parent = null);
        super.new(name, parent);
    endfunction
    
    // Build phase
    function void build_phase(uvm_phase phase);
        super.build_phase(phase);
        
        // Get configuration
        uvm_config_db#(int)::get(this, "", "max_outstanding_reqs", max_outstanding_reqs);
        uvm_config_db#(bit)::get(this, "", "enable_flow_control", enable_flow_control);
        
        `uvm_info("TEMP_SEQ", 
                 $sformatf("Sequencer configured: max_outstanding=%0d, flow_control=%0b", 
                          max_outstanding_reqs, enable_flow_control), 
                 UVM_MEDIUM)
    endfunction
    
    // Override get_next_item to implement flow control if needed
    task get_next_item(output REQ t);
        total_requests++;
        
        if (enable_flow_control) begin
            // Check if we can accept more requests
            while (num_reqs_sent - num_rsps_received >= max_outstanding_reqs) begin
                `uvm_info("TEMP_SEQ", "Flow control: waiting for response", UVM_DEBUG)
                wait_for_item_done();
            end
        end
        
        super.get_next_item(t);
        
        // Track request types
        case (t.txn_type)
            temp_transaction::TEMP_UPDATE: temp_update_requests++;
            temp_transaction::TEMP_SWEEP: sweep_requests++;
            temp_transaction::TIMER_TRIGGER: timer_requests++;
            temp_transaction::TEMP_RAMP: ramp_requests++;
            temp_transaction::TEMP_SPIKE: spike_requests++;
        endcase
        
        `uvm_info("TEMP_SEQ", 
                 $sformatf("Dispatching %s request #%0d", t.txn_type.name(), total_requests), 
                 UVM_HIGH)
    endtask
    
    // Override item_done to track completions
    function void item_done(RSP item = null);
        completed_requests++;
        super.item_done(item);
        
        `uvm_info("TEMP_SEQ", 
                 $sformatf("Temperature request completed (%0d/%0d)", 
                          completed_requests, total_requests), 
                 UVM_HIGH)
    endfunction
    
    // Utility function to check if sequencer is idle
    function bit is_idle();
        return (num_reqs_sent == num_rsps_received);
    endfunction
    
    // Utility function to get pending request count
    function int get_pending_count();
        return (num_reqs_sent - num_rsps_received);
    endfunction
    
    // Report phase
    function void report_phase(uvm_phase phase);
        `uvm_info("TEMP_SEQ", "=== Temperature Sequencer Statistics ===", UVM_LOW)
        `uvm_info("TEMP_SEQ", $sformatf("Total requests: %0d", total_requests), UVM_LOW)
        `uvm_info("TEMP_SEQ", $sformatf("Completed requests: %0d", completed_requests), UVM_LOW)
        `uvm_info("TEMP_SEQ", $sformatf("  Temperature updates: %0d", temp_update_requests), UVM_LOW)
        `uvm_info("TEMP_SEQ", $sformatf("  Temperature sweeps: %0d", sweep_requests), UVM_LOW)
        `uvm_info("TEMP_SEQ", $sformatf("  Timer triggers: %0d", timer_requests), UVM_LOW)
        `uvm_info("TEMP_SEQ", $sformatf("  Temperature ramps: %0d", ramp_requests), UVM_LOW)
        `uvm_info("TEMP_SEQ", $sformatf("  Temperature spikes: %0d", spike_requests), UVM_LOW)
        
        if (total_requests != completed_requests) begin
            `uvm_warning("TEMP_SEQ", 
                        $sformatf("Mismatch: %0d requests sent, %0d completed", 
                                 total_requests, completed_requests))
        end
    endfunction
    
endclass : temp_sequencer

//==============================================================================
// Temperature sequence base class
//==============================================================================

class temp_base_sequence extends uvm_sequence #(temp_transaction);
    
    `uvm_object_utils(temp_base_sequence)
    
    // Configuration
    int num_transactions = 1;
    bit randomize_timing = 1;
    bit [7:0] base_temp = 25;  // Base temperature for sequences
    
    // Constructor
    function new(string name = "temp_base_sequence");
        super.new(name);
    endfunction
    
    // Pre-body task for common setup
    task pre_body();
        if (starting_phase != null) begin
            starting_phase.raise_objection(this, "Temperature sequence starting");
        end
    endtask
    
    // Post-body task for common cleanup
    task post_body();
        if (starting_phase != null) begin
            starting_phase.drop_objection(this, "Temperature sequence completed");
        end
    endtask
    
    // Body task - to be overridden by derived classes
    virtual task body();
        `uvm_error("TEMP_BASE_SEQ", "Body task must be implemented in derived class")
    endtask
    
endclass : temp_base_sequence

//==============================================================================
// Simple temperature sequence
//==============================================================================

class temp_simple_sequence extends temp_base_sequence;
    
    `uvm_object_utils(temp_simple_sequence)
    
    // Constructor
    function new(string name = "temp_simple_sequence");
        super.new(name);
    endfunction
    
    // Body task
    virtual task body();
        temp_transaction req;
        
        repeat(num_transactions) begin
            req = temp_transaction::type_id::create("temp_req");
            
            start_item(req);
            
            if (!req.randomize()) begin
                `uvm_error("TEMP_SEQ", "Randomization failed")
            end
            
            // Override with base temperature if needed
            if (req.txn_type == temp_transaction::TEMP_UPDATE) begin
                req.temp = base_temp;
            end
            
            if (randomize_timing) begin
                // Add some randomization to timing
                if (!req.randomize(pre_delay, hold_cycles, vld_duration)) begin
                    `uvm_warning("TEMP_SEQ", "Timing randomization failed")
                end
            end
            
            `uvm_info("TEMP_SEQ", 
                     $sformatf("Sending temperature transaction:\n%s", req.convert2string()), 
                     UVM_HIGH)
            
            finish_item(req);
        end
    endtask
    
endclass : temp_simple_sequence

//==============================================================================
// Temperature sweep sequence
//==============================================================================

class temp_sweep_sequence extends temp_base_sequence;
    
    `uvm_object_utils(temp_sweep_sequence)
    
    // Sweep parameters
    rand bit [7:0] sweep_start = 20;
    rand bit [7:0] sweep_end = 60;
    rand int sweep_steps = 20;
    
    // Constructor
    function new(string name = "temp_sweep_sequence");
        super.new(name);
    endfunction
    
    // Body task
    virtual task body();
        temp_transaction req;
        
        req = temp_transaction::type_id::create("temp_sweep_req");
        
        start_item(req);
        
        // Configure as sweep transaction
        req.txn_type = temp_transaction::TEMP_SWEEP;
        req.start_temp = sweep_start;
        req.end_temp = sweep_end;
        req.sweep_steps = sweep_steps;
        req.temp_vld = 1;
        
        if (!req.randomize(step_delay, vld_duration)) begin
            `uvm_warning("TEMP_SEQ", "Sweep timing randomization failed")
        end
        
        `uvm_info("TEMP_SEQ", 
                 $sformatf("Sending temperature sweep:\n%s", req.convert2string()), 
                 UVM_MEDIUM)
        
        finish_item(req);
    endtask
    
endclass : temp_sweep_sequence
