//==============================================================================
// File: temp_monitor.sv
// Description: Temperature monitor for runtime temperature monitoring
//==============================================================================

class temp_monitor extends uvm_monitor;
    
    `uvm_component_utils(temp_monitor)
    
    // Virtual interface
    virtual vcom_temp_comp_if.monitor_mp vif;
    
    // Analysis port
    uvm_analysis_port #(temp_transaction) ap;
    
    // Configuration
    bit enable_checks = 1;
    bit enable_coverage = 1;
    bit monitor_continuous = 1;  // Monitor all temperature changes
    
    // Statistics
    int total_temp_changes = 0;
    int total_temp_vld_events = 0;
    int total_timer_events = 0;
    int protocol_violations = 0;
    
    // Temperature tracking
    bit [7:0] prev_temp = 0;
    bit [7:0] min_temp = 255;
    bit [7:0] max_temp = 0;
    real avg_temp = 0;
    int temp_samples = 0;
    
    // Coverage
    covergroup temp_cg;
        option.per_instance = 1;
        
        temp_cp: coverpoint vif.monitor_cb.temp {
            bins very_cold[] = {[0:10]};
            bins cold[] = {[11:20]};
            bins cool[] = {[21:30]};
            bins normal[] = {[31:40]};
            bins warm[] = {[41:50]};
            bins hot[] = {[51:70]};
            bins very_hot[] = {[71:255]};
        }
        
        temp_vld_cp: coverpoint vif.monitor_cb.temp_vld {
            bins inactive = {0};
            bins active = {1};
        }
        
        timer_vld_cp: coverpoint vif.monitor_cb.timer_1s_vld {
            bins inactive = {0};
            bins active = {1};
        }
        
        // Temperature change patterns
        temp_change_cp: coverpoint (vif.monitor_cb.temp - prev_temp) {
            bins no_change = {0};
            bins small_inc[] = {[1:5]};
            bins medium_inc[] = {[6:15]};
            bins large_inc[] = {[16:50]};
            bins huge_inc[] = {[51:255]};
            bins small_dec[] = {[-5:-1]};
            bins medium_dec[] = {[-15:-6]};
            bins large_dec[] = {[-50:-16]};
            bins huge_dec[] = {[-255:-51]};
        }
        
        // Cross coverage
        temp_vld_cross: cross temp_cp, temp_vld_cp;
        temp_change_vld_cross: cross temp_change_cp, temp_vld_cp;
        vld_signals_cross: cross temp_vld_cp, timer_vld_cp {
            // Both signals should not be active simultaneously
            illegal_bins both_active = binsof(temp_vld_cp.active) && 
                                      binsof(timer_vld_cp.active);
        }
    endgroup
    
    // Constructor
    function new(string name = "temp_monitor", uvm_component parent = null);
        super.new(name, parent);
        ap = new("ap", this);
        if (enable_coverage) temp_cg = new();
    endfunction
    
    // Build phase
    function void build_phase(uvm_phase phase);
        super.build_phase(phase);
        
        // Get virtual interface
        if (!uvm_config_db#(virtual vcom_temp_comp_if)::get(
            this, "", "vif", vif)) begin
            `uvm_fatal("TEMP_MON", "Virtual interface not found")
        end
        
        // Get configuration
        uvm_config_db#(bit)::get(this, "", "enable_checks", enable_checks);
        uvm_config_db#(bit)::get(this, "", "enable_coverage", enable_coverage);
        uvm_config_db#(bit)::get(this, "", "monitor_continuous", monitor_continuous);
    endfunction
    
    // Run phase
    task run_phase(uvm_phase phase);
        fork
            monitor_temp_vld_events();
            monitor_timer_events();
            if (monitor_continuous) monitor_continuous_temp();
            if (enable_checks) monitor_protocol_violations();
        join
    endtask
    
    // Monitor temperature valid events
    task monitor_temp_vld_events();
        temp_transaction txn;
        
        // Wait for reset
        @(posedge vif.rst_n);
        repeat(2) @(vif.monitor_cb);
        
        forever begin
            // Wait for temp_vld assertion
            @(posedge vif.monitor_cb.temp_vld);
            
            // Create transaction
            txn = temp_transaction::type_id::create("temp_vld_txn");
            txn.start_time = $time;
            txn.txn_type = temp_transaction::TEMP_UPDATE;
            
            // Capture temperature at the start of valid period
            txn.temp = vif.monitor_cb.temp;
            txn.temp_vld = 1;
            txn.timer_1s_vld = vif.monitor_cb.timer_1s_vld;
            
            // Wait for temp_vld deassertion
            @(negedge vif.monitor_cb.temp_vld);
            txn.end_time = $time;
            txn.vld_duration = int'((txn.end_time - txn.start_time) / CLK_PERIOD);
            txn.completed = 1;
            
            total_temp_vld_events++;
            
            `uvm_info("TEMP_MON", 
                     $sformatf("Temperature valid event: %0d°C for %0d cycles", 
                              txn.temp, txn.vld_duration), 
                     UVM_HIGH)
            
            // Send to analysis port
            ap.write(txn);
            
            // Sample coverage
            if (enable_coverage) temp_cg.sample();
        end
    endtask
    
    // Monitor timer events
    task monitor_timer_events();
        temp_transaction txn;
        
        // Wait for reset
        @(posedge vif.rst_n);
        repeat(2) @(vif.monitor_cb);
        
        forever begin
            // Wait for timer_1s_vld assertion
            @(posedge vif.monitor_cb.timer_1s_vld);
            
            // Create transaction
            txn = temp_transaction::type_id::create("timer_txn");
            txn.start_time = $time;
            txn.txn_type = temp_transaction::TIMER_TRIGGER;
            
            // Capture current state
            txn.temp = vif.monitor_cb.temp;
            txn.temp_vld = vif.monitor_cb.temp_vld;
            txn.timer_1s_vld = 1;
            
            // Wait for timer_1s_vld deassertion
            @(negedge vif.monitor_cb.timer_1s_vld);
            txn.end_time = $time;
            txn.vld_duration = int'((txn.end_time - txn.start_time) / CLK_PERIOD);
            txn.completed = 1;
            
            total_timer_events++;
            
            `uvm_info("TEMP_MON", 
                     $sformatf("Timer event detected for %0d cycles", txn.vld_duration), 
                     UVM_HIGH)
            
            // Send to analysis port
            ap.write(txn);
            
            // Sample coverage
            if (enable_coverage) temp_cg.sample();
        end
    endtask
    
    // Monitor continuous temperature changes
    task monitor_continuous_temp();
        temp_transaction txn;
        
        // Wait for reset
        @(posedge vif.rst_n);
        repeat(2) @(vif.monitor_cb);
        
        // Initialize previous temperature
        prev_temp = vif.monitor_cb.temp;
        min_temp = prev_temp;
        max_temp = prev_temp;
        avg_temp = real'(prev_temp);
        temp_samples = 1;
        
        forever begin
            @(vif.monitor_cb);
            
            // Update statistics
            temp_samples++;
            avg_temp = ((avg_temp * real'(temp_samples - 1)) + real'(vif.monitor_cb.temp)) / real'(temp_samples);
            
            if (vif.monitor_cb.temp < min_temp) min_temp = vif.monitor_cb.temp;
            if (vif.monitor_cb.temp > max_temp) max_temp = vif.monitor_cb.temp;
            
            // Check for temperature change
            if (vif.monitor_cb.temp != prev_temp) begin
                // Create transaction for temperature change
                txn = temp_transaction::type_id::create("temp_change_txn");
                txn.start_time = $time;
                txn.txn_type = temp_transaction::TEMP_UPDATE;
                txn.temp = vif.monitor_cb.temp;
                txn.temp_vld = vif.monitor_cb.temp_vld;
                txn.timer_1s_vld = vif.monitor_cb.timer_1s_vld;
                txn.end_time = $time;
                txn.completed = 1;
                
                total_temp_changes++;
                
                `uvm_info("TEMP_MON", 
                         $sformatf("Temperature changed: %0d°C -> %0d°C (Δ%+d)", 
                                  prev_temp, vif.monitor_cb.temp, 
                                  int'(vif.monitor_cb.temp) - int'(prev_temp)), 
                         UVM_DEBUG)
                
                // Send to analysis port
                ap.write(txn);
                
                prev_temp = vif.monitor_cb.temp;
            end
            
            // Sample coverage
            if (enable_coverage) temp_cg.sample();
        end
    endtask
    
    // Monitor protocol violations
    task monitor_protocol_violations();
        forever begin
            @(vif.monitor_cb);
            
            // Check for simultaneous temp_vld and timer_1s_vld
            if (vif.monitor_cb.temp_vld && vif.monitor_cb.timer_1s_vld) begin
                `uvm_warning("TEMP_MON", 
                            "Protocol violation: temp_vld and timer_1s_vld both active")
                protocol_violations++;
            end
            
            // Check for temperature stability during temp_vld
            if (vif.monitor_cb.temp_vld) begin
                bit [7:0] stable_temp = vif.monitor_cb.temp;
                @(vif.monitor_cb);
                while (vif.monitor_cb.temp_vld) begin
                    if (vif.monitor_cb.temp != stable_temp) begin
                        `uvm_warning("TEMP_MON", 
                                    $sformatf("Temperature changed during temp_vld: %0d -> %0d", 
                                             stable_temp, vif.monitor_cb.temp))
                        protocol_violations++;
                        stable_temp = vif.monitor_cb.temp;
                    end
                    @(vif.monitor_cb);
                end
            end
        end
    endtask
    
    // Check phase
    function void check_phase(uvm_phase phase);
        if (enable_checks && protocol_violations > 0) begin
            `uvm_warning("TEMP_MON", 
                        $sformatf("Detected %0d protocol violations", protocol_violations))
        end
    endfunction
    
    // Report phase
    function void report_phase(uvm_phase phase);
        `uvm_info("TEMP_MON", "=== Temperature Monitor Statistics ===", UVM_LOW)
        `uvm_info("TEMP_MON", $sformatf("Temperature changes: %0d", total_temp_changes), UVM_LOW)
        `uvm_info("TEMP_MON", $sformatf("Temperature valid events: %0d", total_temp_vld_events), UVM_LOW)
        `uvm_info("TEMP_MON", $sformatf("Timer events: %0d", total_timer_events), UVM_LOW)
        `uvm_info("TEMP_MON", $sformatf("Protocol violations: %0d", protocol_violations), UVM_LOW)
        
        if (temp_samples > 0) begin
            `uvm_info("TEMP_MON", $sformatf("Temperature range: %0d°C - %0d°C", min_temp, max_temp), UVM_LOW)
            `uvm_info("TEMP_MON", $sformatf("Average temperature: %.1f°C", avg_temp), UVM_LOW)
        end
        
        if (enable_coverage) begin
            `uvm_info("TEMP_MON", 
                     $sformatf("Temperature coverage: %.2f%%", temp_cg.get_coverage()), 
                     UVM_LOW)
        end
    endfunction
    
endclass : temp_monitor
