//==============================================================================
// File: temp_transaction.sv
// Description: Temperature transaction for runtime temperature stimuli
//==============================================================================

class temp_transaction extends uvm_sequence_item;
    
    // Temperature data
    rand bit [7:0] temp;           // Current temperature value
    rand bit       temp_vld;       // Temperature valid signal
    rand bit       timer_1s_vld;   // 1-second timer valid signal
    
    // Timing control
    rand int       pre_delay;      // Delay before temperature update
    rand int       hold_cycles;    // Cycles to hold temperature
    rand int       vld_duration;   // Duration of valid signals
    
    // Transaction type
    typedef enum {
        TEMP_UPDATE,     // Normal temperature update
        TEMP_SWEEP,      // Temperature sweep
        TIMER_TRIGGER,   // Timer-based trigger
        TEMP_RAMP,       // Temperature ramp
        TEMP_SPIKE       // Temperature spike
    } temp_txn_type_e;
    
    rand temp_txn_type_e txn_type;
    
    // Sweep parameters (for TEMP_SWEEP type)
    rand bit [7:0] start_temp;
    rand bit [7:0] end_temp;
    rand int       sweep_steps;
    rand int       step_delay;
    
    // Ramp parameters (for TEMP_RAMP type)
    rand int       ramp_rate;      // Temperature change per cycle
    rand int       ramp_duration;  // Duration of ramp
    
    // Spike parameters (for TEMP_SPIKE type)
    rand bit [7:0] spike_temp;
    rand int       spike_duration;
    
    // Transaction metadata
    time           start_time;
    time           end_time;
    bit            completed;
    
    `uvm_object_utils_begin(temp_transaction)
        `uvm_field_int(temp, UVM_ALL_ON)
        `uvm_field_int(temp_vld, UVM_ALL_ON)
        `uvm_field_int(timer_1s_vld, UVM_ALL_ON)
        `uvm_field_int(pre_delay, UVM_ALL_ON)
        `uvm_field_int(hold_cycles, UVM_ALL_ON)
        `uvm_field_int(vld_duration, UVM_ALL_ON)
        `uvm_field_enum(temp_txn_type_e, txn_type, UVM_ALL_ON)
        `uvm_field_int(start_temp, UVM_ALL_ON)
        `uvm_field_int(end_temp, UVM_ALL_ON)
        `uvm_field_int(sweep_steps, UVM_ALL_ON)
        `uvm_field_int(step_delay, UVM_ALL_ON)
        `uvm_field_int(ramp_rate, UVM_ALL_ON)
        `uvm_field_int(ramp_duration, UVM_ALL_ON)
        `uvm_field_int(spike_temp, UVM_ALL_ON)
        `uvm_field_int(spike_duration, UVM_ALL_ON)
        `uvm_field_int(start_time, UVM_ALL_ON | UVM_TIME)
        `uvm_field_int(end_time, UVM_ALL_ON | UVM_TIME)
        `uvm_field_int(completed, UVM_ALL_ON)
    `uvm_object_utils_end
    
    // Constraints
    
    // Valid temperature range
    constraint valid_temp_c {
        temp inside {[0:255]};
        start_temp inside {[0:255]};
        end_temp inside {[0:255]};
        spike_temp inside {[0:255]};
    }
    
    // Reasonable temperature range for normal operation
    constraint reasonable_temp_c {
        temp inside {[10:80]};  // 10°C to 80°C
    }
    
    // Timing constraints
    constraint timing_c {
        pre_delay inside {[0:50]};
        hold_cycles inside {[1:100]};
        vld_duration inside {[1:10]};
        step_delay inside {[1:20]};
        ramp_duration inside {[10:100]};
        spike_duration inside {[5:50]};
    }
    
    // Sweep constraints
    constraint sweep_c {
        if (txn_type == TEMP_SWEEP) {
            sweep_steps inside {[5:50]};
            start_temp != end_temp;
        }
    }
    
    // Ramp constraints
    constraint ramp_c {
        if (txn_type == TEMP_RAMP) {
            ramp_rate inside {[1:5]};  // 1-5 degrees per cycle
        }
    }
    
    // Transaction type distribution
    constraint txn_type_dist_c {
        txn_type dist {
            TEMP_UPDATE := 50,
            TEMP_SWEEP := 20,
            TIMER_TRIGGER := 15,
            TEMP_RAMP := 10,
            TEMP_SPIKE := 5
        };
    }
    
    // Valid signal constraints
    constraint valid_signals_c {
        // temp_vld should be asserted for temperature updates
        if (txn_type inside {TEMP_UPDATE, TEMP_SWEEP, TEMP_RAMP, TEMP_SPIKE}) {
            temp_vld == 1;
        }
        
        // timer_1s_vld should be asserted for timer triggers
        if (txn_type == TIMER_TRIGGER) {
            timer_1s_vld == 1;
            temp_vld == 0;  // Usually mutually exclusive
        }
    }
    
    // Constructor
    function new(string name = "temp_transaction");
        super.new(name);
    endfunction
    
    // Validation function
    function bit is_valid();
        case (txn_type)
            TEMP_SWEEP: begin
                if (sweep_steps == 0) begin
                    `uvm_error("TEMP_TXN", "Sweep steps cannot be zero")
                    return 0;
                end
            end
            TEMP_RAMP: begin
                if (ramp_rate == 0 || ramp_duration == 0) begin
                    `uvm_error("TEMP_TXN", "Ramp parameters cannot be zero")
                    return 0;
                end
            end
            TEMP_SPIKE: begin
                if (spike_duration == 0) begin
                    `uvm_error("TEMP_TXN", "Spike duration cannot be zero")
                    return 0;
                end
            end
        endcase
        return 1;
    endfunction
    
    // Calculate expected temperature change for ramp
    function bit [7:0] calc_ramp_temp(int step);
        int temp_change = ramp_rate * step;
        int new_temp = int'(temp) + temp_change;
        
        // Clamp to valid range
        if (new_temp > 255) new_temp = 255;
        if (new_temp < 0) new_temp = 0;
        
        return bit'[7:0](new_temp);
    endfunction
    
    // Calculate temperature for sweep step
    function bit [7:0] calc_sweep_temp(int step);
        real progress = real'(step) / real'(sweep_steps - 1);
        real temp_range = real'(end_temp) - real'(start_temp);
        real new_temp = real'(start_temp) + (progress * temp_range);
        
        int temp_int = int'(new_temp);
        if (temp_int > 255) temp_int = 255;
        if (temp_int < 0) temp_int = 0;
        
        return bit'[7:0](temp_int);
    endfunction
    
    // Pretty print function
    function string convert2string();
        string s;
        s = $sformatf("Temperature Transaction (%s):\n", txn_type.name());
        s = {s, $sformatf("  temp = %0d°C (0x%02h)\n", temp, temp)};
        s = {s, $sformatf("  temp_vld = %0b\n", temp_vld)};
        s = {s, $sformatf("  timer_1s_vld = %0b\n", timer_1s_vld)};
        s = {s, $sformatf("  pre_delay = %0d cycles\n", pre_delay)};
        s = {s, $sformatf("  hold_cycles = %0d cycles\n", hold_cycles)};
        s = {s, $sformatf("  vld_duration = %0d cycles\n", vld_duration)};
        
        case (txn_type)
            TEMP_SWEEP: begin
                s = {s, $sformatf("  Sweep: %0d°C -> %0d°C (%0d steps)\n", 
                                 start_temp, end_temp, sweep_steps)};
            end
            TEMP_RAMP: begin
                s = {s, $sformatf("  Ramp: rate=%0d°C/cycle, duration=%0d cycles\n", 
                                 ramp_rate, ramp_duration)};
            end
            TEMP_SPIKE: begin
                s = {s, $sformatf("  Spike: %0d°C for %0d cycles\n", 
                                 spike_temp, spike_duration)};
            end
        endcase
        
        if (completed) begin
            s = {s, $sformatf("  Duration = %0t\n", end_time - start_time)};
        end
        return s;
    endfunction
    
    // Copy function
    function void do_copy(uvm_object rhs);
        temp_transaction rhs_;
        if (!$cast(rhs_, rhs)) begin
            `uvm_fatal("TEMP_TXN", "Cast failed in do_copy")
        end
        super.do_copy(rhs);
        temp = rhs_.temp;
        temp_vld = rhs_.temp_vld;
        timer_1s_vld = rhs_.timer_1s_vld;
        pre_delay = rhs_.pre_delay;
        hold_cycles = rhs_.hold_cycles;
        vld_duration = rhs_.vld_duration;
        txn_type = rhs_.txn_type;
        start_temp = rhs_.start_temp;
        end_temp = rhs_.end_temp;
        sweep_steps = rhs_.sweep_steps;
        step_delay = rhs_.step_delay;
        ramp_rate = rhs_.ramp_rate;
        ramp_duration = rhs_.ramp_duration;
        spike_temp = rhs_.spike_temp;
        spike_duration = rhs_.spike_duration;
        start_time = rhs_.start_time;
        end_time = rhs_.end_time;
        completed = rhs_.completed;
    endfunction
    
    // Compare function
    function bit do_compare(uvm_object rhs, uvm_comparer comparer);
        temp_transaction rhs_;
        if (!$cast(rhs_, rhs)) return 0;
        
        return (super.do_compare(rhs, comparer) &&
                (temp == rhs_.temp) &&
                (temp_vld == rhs_.temp_vld) &&
                (timer_1s_vld == rhs_.timer_1s_vld) &&
                (txn_type == rhs_.txn_type));
    endfunction
    
endclass : temp_transaction
