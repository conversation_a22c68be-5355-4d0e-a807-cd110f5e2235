//==============================================================================
// File: temp_driver.sv
// Description: Temperature driver for runtime temperature stimuli
//==============================================================================

class temp_driver extends uvm_driver #(temp_transaction);
    
    `uvm_component_utils(temp_driver)
    
    // Virtual interface
    virtual vcom_temp_comp_if.temp_mp vif;
    
    // Configuration
    bit enable_continuous_temp = 1;  // Keep temperature stable between transactions
    bit [7:0] default_temp = 25;     // Default temperature (25°C)
    
    // Statistics
    int total_temp_updates = 0;
    int total_timer_triggers = 0;
    int total_sweeps = 0;
    int total_ramps = 0;
    int total_spikes = 0;
    
    // Current state
    bit [7:0] current_temp;
    bit temp_driving = 0;
    
    // Constructor
    function new(string name = "temp_driver", uvm_component parent = null);
        super.new(name, parent);
    endfunction
    
    // Build phase
    function void build_phase(uvm_phase phase);
        super.build_phase(phase);
        
        // Get virtual interface
        if (!uvm_config_db#(virtual vcom_temp_comp_if)::get(
            this, "", "vif", vif)) begin
            `uvm_fatal("TEMP_DRV", "Virtual interface not found")
        end
        
        // Get configuration parameters
        uvm_config_db#(bit)::get(this, "", "enable_continuous_temp", enable_continuous_temp);
        uvm_config_db#(bit [7:0])::get(this, "", "default_temp", default_temp);
        
        current_temp = default_temp;
    endfunction
    
    // Run phase
    task run_phase(uvm_phase phase);
        temp_transaction req;
        
        // Initialize interface
        initialize_interface();
        
        // Wait for reset deassertion
        wait_for_reset();
        
        // Start background temperature maintenance if enabled
        if (enable_continuous_temp) begin
            fork
                maintain_temperature();
            join_none
        end
        
        forever begin
            // Get next transaction
            seq_item_port.get_next_item(req);
            
            `uvm_info("TEMP_DRV", 
                     $sformatf("Driving temperature transaction:\n%s", req.convert2string()), 
                     UVM_MEDIUM)
            
            // Drive the transaction
            drive_transaction(req);
            
            // Mark transaction as done
            seq_item_port.item_done();
        end
    endtask
    
    // Initialize interface signals
    task initialize_interface();
        vif.temp_cb.temp <= default_temp;
        vif.temp_cb.temp_vld <= 1'b0;
        vif.temp_cb.timer_1s_vld <= 1'b0;
        current_temp = default_temp;
        
        `uvm_info("TEMP_DRV", 
                 $sformatf("Interface initialized with default temp = %0d°C", default_temp), 
                 UVM_HIGH)
    endtask
    
    // Wait for reset deassertion
    task wait_for_reset();
        @(posedge vif.rst_n);
        repeat(2) @(vif.temp_cb);  // Wait a few cycles after reset
        `uvm_info("TEMP_DRV", "Reset deassertion detected", UVM_HIGH)
    endtask
    
    // Drive a temperature transaction
    task drive_transaction(temp_transaction req);
        time start_time, end_time;
        
        start_time = $time;
        req.start_time = start_time;
        temp_driving = 1;
        
        // Pre-transaction delay
        if (req.pre_delay > 0) begin
            repeat(req.pre_delay) @(vif.temp_cb);
        end
        
        // Drive based on transaction type
        case (req.txn_type)
            temp_transaction::TEMP_UPDATE: drive_temp_update(req);
            temp_transaction::TEMP_SWEEP: drive_temp_sweep(req);
            temp_transaction::TIMER_TRIGGER: drive_timer_trigger(req);
            temp_transaction::TEMP_RAMP: drive_temp_ramp(req);
            temp_transaction::TEMP_SPIKE: drive_temp_spike(req);
            default: begin
                `uvm_error("TEMP_DRV", $sformatf("Unknown transaction type: %s", req.txn_type.name()))
            end
        endcase
        
        temp_driving = 0;
        end_time = $time;
        req.end_time = end_time;
        req.completed = 1;
        
        `uvm_info("TEMP_DRV", 
                 $sformatf("Transaction completed in %0t", end_time - start_time), 
                 UVM_HIGH)
    endtask
    
    // Drive simple temperature update
    task drive_temp_update(temp_transaction req);
        // Set temperature
        vif.temp_cb.temp <= req.temp;
        current_temp = req.temp;
        @(vif.temp_cb);
        
        // Assert temp_vld
        if (req.temp_vld) begin
            vif.temp_cb.temp_vld <= 1'b1;
            repeat(req.vld_duration) @(vif.temp_cb);
            vif.temp_cb.temp_vld <= 1'b0;
        end
        
        // Hold temperature
        repeat(req.hold_cycles) @(vif.temp_cb);
        
        total_temp_updates++;
        `uvm_info("TEMP_DRV", $sformatf("Temperature updated to %0d°C", req.temp), UVM_HIGH)
    endtask
    
    // Drive temperature sweep
    task drive_temp_sweep(temp_transaction req);
        bit [7:0] sweep_temp;
        
        for (int i = 0; i < req.sweep_steps; i++) begin
            sweep_temp = req.calc_sweep_temp(i);
            
            // Set temperature
            vif.temp_cb.temp <= sweep_temp;
            current_temp = sweep_temp;
            @(vif.temp_cb);
            
            // Assert temp_vld
            if (req.temp_vld) begin
                vif.temp_cb.temp_vld <= 1'b1;
                repeat(req.vld_duration) @(vif.temp_cb);
                vif.temp_cb.temp_vld <= 1'b0;
            end
            
            // Step delay
            repeat(req.step_delay) @(vif.temp_cb);
            
            `uvm_info("TEMP_DRV", 
                     $sformatf("Sweep step %0d/%0d: %0d°C", i+1, req.sweep_steps, sweep_temp), 
                     UVM_DEBUG)
        end
        
        total_sweeps++;
        `uvm_info("TEMP_DRV", 
                 $sformatf("Temperature sweep completed: %0d°C -> %0d°C", 
                          req.start_temp, req.end_temp), 
                 UVM_HIGH)
    endtask
    
    // Drive timer trigger
    task drive_timer_trigger(temp_transaction req);
        // Assert timer_1s_vld
        vif.temp_cb.timer_1s_vld <= 1'b1;
        repeat(req.vld_duration) @(vif.temp_cb);
        vif.temp_cb.timer_1s_vld <= 1'b0;
        
        // Hold for specified cycles
        repeat(req.hold_cycles) @(vif.temp_cb);
        
        total_timer_triggers++;
        `uvm_info("TEMP_DRV", "Timer trigger driven", UVM_HIGH)
    endtask
    
    // Drive temperature ramp
    task drive_temp_ramp(temp_transaction req);
        bit [7:0] ramp_temp;
        
        for (int i = 0; i < req.ramp_duration; i++) begin
            ramp_temp = req.calc_ramp_temp(i);
            
            // Set temperature
            vif.temp_cb.temp <= ramp_temp;
            current_temp = ramp_temp;
            @(vif.temp_cb);
            
            // Assert temp_vld periodically
            if (req.temp_vld && (i % 5 == 0)) begin  // Every 5 cycles
                vif.temp_cb.temp_vld <= 1'b1;
                repeat(req.vld_duration) @(vif.temp_cb);
                vif.temp_cb.temp_vld <= 1'b0;
            end
            
            `uvm_info("TEMP_DRV", 
                     $sformatf("Ramp step %0d/%0d: %0d°C", i+1, req.ramp_duration, ramp_temp), 
                     UVM_DEBUG)
        end
        
        total_ramps++;
        `uvm_info("TEMP_DRV", 
                 $sformatf("Temperature ramp completed: rate=%0d°C/cycle", req.ramp_rate), 
                 UVM_HIGH)
    endtask
    
    // Drive temperature spike
    task drive_temp_spike(temp_transaction req);
        bit [7:0] original_temp = current_temp;
        
        // Set spike temperature
        vif.temp_cb.temp <= req.spike_temp;
        current_temp = req.spike_temp;
        @(vif.temp_cb);
        
        // Assert temp_vld
        if (req.temp_vld) begin
            vif.temp_cb.temp_vld <= 1'b1;
            repeat(req.vld_duration) @(vif.temp_cb);
            vif.temp_cb.temp_vld <= 1'b0;
        end
        
        // Hold spike
        repeat(req.spike_duration) @(vif.temp_cb);
        
        // Return to original temperature
        vif.temp_cb.temp <= original_temp;
        current_temp = original_temp;
        @(vif.temp_cb);
        
        // Assert temp_vld for return
        if (req.temp_vld) begin
            vif.temp_cb.temp_vld <= 1'b1;
            repeat(req.vld_duration) @(vif.temp_cb);
            vif.temp_cb.temp_vld <= 1'b0;
        end
        
        total_spikes++;
        `uvm_info("TEMP_DRV", 
                 $sformatf("Temperature spike completed: %0d°C -> %0d°C -> %0d°C", 
                          original_temp, req.spike_temp, original_temp), 
                 UVM_HIGH)
    endtask
    
    // Background task to maintain temperature when not actively driving
    task maintain_temperature();
        forever begin
            @(vif.temp_cb);
            if (!temp_driving) begin
                vif.temp_cb.temp <= current_temp;
            end
        end
    endtask
    
    // Report phase
    function void report_phase(uvm_phase phase);
        `uvm_info("TEMP_DRV", "=== Temperature Driver Statistics ===", UVM_LOW)
        `uvm_info("TEMP_DRV", $sformatf("Temperature updates: %0d", total_temp_updates), UVM_LOW)
        `uvm_info("TEMP_DRV", $sformatf("Timer triggers: %0d", total_timer_triggers), UVM_LOW)
        `uvm_info("TEMP_DRV", $sformatf("Temperature sweeps: %0d", total_sweeps), UVM_LOW)
        `uvm_info("TEMP_DRV", $sformatf("Temperature ramps: %0d", total_ramps), UVM_LOW)
        `uvm_info("TEMP_DRV", $sformatf("Temperature spikes: %0d", total_spikes), UVM_LOW)
        `uvm_info("TEMP_DRV", $sformatf("Final temperature: %0d°C", current_temp), UVM_LOW)
    endfunction
    
endclass : temp_driver
