//==============================================================================
// File: temp_agent.sv
// Description: Temperature agent for runtime temperature stimuli
//==============================================================================

class temp_agent extends uvm_agent;
    
    `uvm_component_utils(temp_agent)
    
    // Agent components
    temp_driver    driver;
    temp_monitor   monitor;
    temp_sequencer sequencer;
    
    // Analysis port
    uvm_analysis_port #(temp_transaction) ap;
    
    // Configuration
    uvm_active_passive_enum is_active = UVM_ACTIVE;
    bit enable_coverage = 1;
    bit enable_checks = 1;
    bit enable_continuous_temp = 1;
    bit monitor_continuous = 1;
    bit [7:0] default_temp = 25;
    
    // Constructor
    function new(string name = "temp_agent", uvm_component parent = null);
        super.new(name, parent);
    endfunction
    
    // Build phase
    function void build_phase(uvm_phase phase);
        super.build_phase(phase);
        
        // Get configuration
        uvm_config_db#(uvm_active_passive_enum)::get(this, "", "is_active", is_active);
        uvm_config_db#(bit)::get(this, "", "enable_coverage", enable_coverage);
        uvm_config_db#(bit)::get(this, "", "enable_checks", enable_checks);
        uvm_config_db#(bit)::get(this, "", "enable_continuous_temp", enable_continuous_temp);
        uvm_config_db#(bit)::get(this, "", "monitor_continuous", monitor_continuous);
        uvm_config_db#(bit [7:0])::get(this, "", "default_temp", default_temp);
        
        // Create monitor (always present)
        monitor = temp_monitor::type_id::create("monitor", this);
        
        // Set monitor configuration
        uvm_config_db#(bit)::set(this, "monitor", "enable_coverage", enable_coverage);
        uvm_config_db#(bit)::set(this, "monitor", "enable_checks", enable_checks);
        uvm_config_db#(bit)::set(this, "monitor", "monitor_continuous", monitor_continuous);
        
        // Create active components if agent is active
        if (is_active == UVM_ACTIVE) begin
            driver = temp_driver::type_id::create("driver", this);
            sequencer = temp_sequencer::type_id::create("sequencer", this);
            
            // Set driver configuration
            uvm_config_db#(bit)::set(this, "driver", "enable_continuous_temp", enable_continuous_temp);
            uvm_config_db#(bit [7:0])::set(this, "driver", "default_temp", default_temp);
        end
        
        `uvm_info("TEMP_AGENT", 
                 $sformatf("Agent created: is_active=%s, coverage=%0b, checks=%0b, default_temp=%0d°C", 
                          is_active.name(), enable_coverage, enable_checks, default_temp), 
                 UVM_MEDIUM)
    endfunction
    
    // Connect phase
    function void connect_phase(uvm_phase phase);
        super.connect_phase(phase);
        
        // Connect analysis port
        ap = monitor.ap;
        
        // Connect driver and sequencer if active
        if (is_active == UVM_ACTIVE) begin
            driver.seq_item_port.connect(sequencer.seq_item_export);
        end
        
        `uvm_info("TEMP_AGENT", "Agent connections completed", UVM_HIGH)
    endfunction
    
    // End of elaboration phase
    function void end_of_elaboration_phase(uvm_phase phase);
        super.end_of_elaboration_phase(phase);
        
        // Print agent configuration
        `uvm_info("TEMP_AGENT", "=== Temperature Agent Setup ===", UVM_MEDIUM)
        `uvm_info("TEMP_AGENT", $sformatf("  Active: %s", is_active.name()), UVM_MEDIUM)
        `uvm_info("TEMP_AGENT", $sformatf("  Coverage: %0b", enable_coverage), UVM_MEDIUM)
        `uvm_info("TEMP_AGENT", $sformatf("  Checks: %0b", enable_checks), UVM_MEDIUM)
        `uvm_info("TEMP_AGENT", $sformatf("  Continuous temp: %0b", enable_continuous_temp), UVM_MEDIUM)
        `uvm_info("TEMP_AGENT", $sformatf("  Monitor continuous: %0b", monitor_continuous), UVM_MEDIUM)
        `uvm_info("TEMP_AGENT", $sformatf("  Default temperature: %0d°C", default_temp), UVM_MEDIUM)
        
        if (is_active == UVM_ACTIVE) begin
            `uvm_info("TEMP_AGENT", "  Components: Driver, Monitor, Sequencer", UVM_MEDIUM)
        end else begin
            `uvm_info("TEMP_AGENT", "  Components: Monitor only", UVM_MEDIUM)
        end
    endfunction
    
    // Run phase
    task run_phase(uvm_phase phase);
        // Agent-level monitoring or coordination tasks can go here
        // For now, just wait for the phase to end
        phase.phase_done.wait_for_all();
    endtask
    
    // Report phase
    function void report_phase(uvm_phase phase);
        `uvm_info("TEMP_AGENT", "=== Temperature Agent Report ===", UVM_LOW)
        
        // Report will be handled by individual components
        // Agent can provide summary information here if needed
    endfunction
    
endclass : temp_agent

//==============================================================================
// Temperature agent configuration object
//==============================================================================

class temp_agent_config extends uvm_object;
    
    `uvm_object_utils(temp_agent_config)
    
    // Configuration parameters
    uvm_active_passive_enum is_active = UVM_ACTIVE;
    bit enable_coverage = 1;
    bit enable_checks = 1;
    bit enable_continuous_temp = 1;
    bit monitor_continuous = 1;
    bit [7:0] default_temp = 25;
    int max_outstanding_reqs = 5;
    bit enable_flow_control = 0;
    
    // Temperature range constraints
    bit [7:0] min_temp = 0;
    bit [7:0] max_temp = 255;
    bit [7:0] typical_temp_low = 15;
    bit [7:0] typical_temp_high = 50;
    
    // Interface handle
    virtual vcom_temp_comp_if vif;
    
    // Constructor
    function new(string name = "temp_agent_config");
        super.new(name);
    endfunction
    
    // Validation function
    function bit is_valid();
        if (min_temp >= max_temp) begin
            `uvm_error("TEMP_CFG", "min_temp must be less than max_temp")
            return 0;
        end
        if (default_temp < min_temp || default_temp > max_temp) begin
            `uvm_error("TEMP_CFG", "default_temp must be within min_temp and max_temp range")
            return 0;
        end
        if (typical_temp_low >= typical_temp_high) begin
            `uvm_error("TEMP_CFG", "typical_temp_low must be less than typical_temp_high")
            return 0;
        end
        return 1;
    endfunction
    
    // Convert to string
    function string convert2string();
        string s;
        s = $sformatf("Temperature Agent Configuration:\n");
        s = {s, $sformatf("  is_active = %s\n", is_active.name())};
        s = {s, $sformatf("  enable_coverage = %0b\n", enable_coverage)};
        s = {s, $sformatf("  enable_checks = %0b\n", enable_checks)};
        s = {s, $sformatf("  enable_continuous_temp = %0b\n", enable_continuous_temp)};
        s = {s, $sformatf("  monitor_continuous = %0b\n", monitor_continuous)};
        s = {s, $sformatf("  default_temp = %0d°C\n", default_temp)};
        s = {s, $sformatf("  max_outstanding_reqs = %0d\n", max_outstanding_reqs)};
        s = {s, $sformatf("  enable_flow_control = %0b\n", enable_flow_control)};
        s = {s, $sformatf("  Temperature range: %0d°C - %0d°C\n", min_temp, max_temp)};
        s = {s, $sformatf("  Typical range: %0d°C - %0d°C\n", typical_temp_low, typical_temp_high)};
        return s;
    endfunction
    
endclass : temp_agent_config
