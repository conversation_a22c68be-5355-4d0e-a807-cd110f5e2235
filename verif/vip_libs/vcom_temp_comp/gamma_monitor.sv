//==============================================================================
// File: gamma_monitor.sv
// Description: Gamma output monitor for temperature compensation verification
//==============================================================================

class gamma_result_transaction extends uvm_sequence_item;
    
    // Gamma compensation result
    bit [13:0] gamma_tcomp;      // (s,9,5) format
    bit        gamma_tcomp_vld;
    
    // Input conditions that led to this result
    bit [7:0] temp_current;
    bit [7:0] temp_ref;
    bit [7:0] gamma_coeff;
    
    // Timing information
    time      timestamp;
    bit       valid;
    
    // Decoded values for analysis
    real      gamma_real;        // Real value of gamma compensation
    bit       is_positive;       // Sign of compensation
    
    `uvm_object_utils_begin(gamma_result_transaction)
        `uvm_field_int(gamma_tcomp, UVM_ALL_ON)
        `uvm_field_int(gamma_tcomp_vld, UVM_ALL_ON)
        `uvm_field_int(temp_current, UVM_ALL_ON)
        `uvm_field_int(temp_ref, UVM_ALL_ON)
        `uvm_field_int(gamma_coeff, UVM_ALL_ON)
        `uvm_field_int(timestamp, UVM_ALL_ON | UVM_TIME)
        `uvm_field_int(valid, UVM_ALL_ON)
        `uvm_field_real(gamma_real, UVM_ALL_ON)
        `uvm_field_int(is_positive, UVM_ALL_ON)
    `uvm_object_utils_end
    
    function new(string name = "gamma_result_transaction");
        super.new(name);
    endfunction
    
    // Decode gamma value from (s,9,5) format to real
    function void decode_gamma();
        bit sign = gamma_tcomp[13];
        bit [12:0] magnitude = gamma_tcomp[12:0];
        gamma_real = real'(magnitude) / 32.0;  // Divide by 2^5
        if (sign) gamma_real = -gamma_real;
        is_positive = !sign;
    endfunction
    
    function string convert2string();
        string s;
        decode_gamma();
        s = $sformatf("Gamma Result Transaction:\n");
        s = {s, $sformatf("  gamma_tcomp = 0x%04h (%.4f)\n", gamma_tcomp, gamma_real)};
        s = {s, $sformatf("  gamma_tcomp_vld = %0b\n", gamma_tcomp_vld)};
        s = {s, $sformatf("  temp_current = %0d°C\n", temp_current)};
        s = {s, $sformatf("  temp_ref = %0d°C\n", temp_ref)};
        s = {s, $sformatf("  gamma_coeff = 0x%02h (%.4f)\n", gamma_coeff, real'(gamma_coeff)/128.0)};
        s = {s, $sformatf("  sign = %s\n", is_positive ? "positive" : "negative")};
        s = {s, $sformatf("  timestamp = %0t\n", timestamp)};
        s = {s, $sformatf("  valid = %0b\n", valid)};
        return s;
    endfunction
    
endclass : gamma_result_transaction

//==============================================================================
// Gamma Monitor Class
//==============================================================================

class gamma_monitor extends uvm_monitor;
    
    `uvm_component_utils(gamma_monitor)
    
    // Virtual interface
    virtual vcom_temp_comp_if.monitor_mp vif;
    
    // Analysis port
    uvm_analysis_port #(gamma_result_transaction) ap;
    
    // Configuration
    bit enable_checks = 1;
    bit enable_coverage = 1;
    bit monitor_all_changes = 1;  // Monitor all gamma changes, not just valid ones
    
    // Statistics
    int total_gamma_updates = 0;
    int gamma_vld_events = 0;
    int positive_compensations = 0;
    int negative_compensations = 0;
    int zero_compensations = 0;
    
    // Gamma tracking
    bit [13:0] prev_gamma_tcomp = 0;
    real min_gamma = 1000.0;
    real max_gamma = -1000.0;
    real avg_gamma = 0;
    int gamma_samples = 0;
    
    // Coverage
    covergroup gamma_cg;
        option.per_instance = 1;
        
        gamma_magnitude_cp: coverpoint vif.monitor_cb.gamma_tcomp[12:0] {
            bins zero = {0};
            bins very_small[] = {[1:31]};      // 0.03125 to 0.96875
            bins small[] = {[32:127]};         // 1.0 to 3.96875
            bins medium[] = {[128:511]};       // 4.0 to 15.96875
            bins large[] = {[512:2047]};       // 16.0 to 63.96875
            bins very_large[] = {[2048:8191]}; // 64.0 to 255.96875
        }
        
        gamma_sign_cp: coverpoint vif.monitor_cb.gamma_tcomp[13] {
            bins positive = {0};
            bins negative = {1};
        }
        
        gamma_vld_cp: coverpoint vif.monitor_cb.gamma_tcomp_vld {
            bins inactive = {0};
            bins active = {1};
        }
        
        temp_cp: coverpoint vif.monitor_cb.temp {
            bins very_cold[] = {[0:10]};
            bins cold[] = {[11:25]};
            bins normal[] = {[26:40]};
            bins warm[] = {[41:60]};
            bins hot[] = {[61:255]};
        }
        
        gamma_coeff_cp: coverpoint vif.monitor_cb.gamma_tcomp_coeff {
            bins small[] = {[1:50]};
            bins medium[] = {[51:150]};
            bins large[] = {[151:255]};
        }
        
        // Temperature delta (approximation)
        temp_delta_cp: coverpoint (vif.monitor_cb.temp - vif.monitor_cb.temp_vcom) {
            bins large_neg[] = {[-50:-21]};
            bins med_neg[] = {[-20:-6]};
            bins small_neg[] = {[-5:-1]};
            bins zero = {0};
            bins small_pos[] = {[1:5]};
            bins med_pos[] = {[6:20]};
            bins large_pos[] = {[21:50]};
        }
        
        // Cross coverage
        sign_magnitude_cross: cross gamma_sign_cp, gamma_magnitude_cp;
        temp_coeff_cross: cross temp_cp, gamma_coeff_cp;
        delta_sign_cross: cross temp_delta_cp, gamma_sign_cp;
        vld_magnitude_cross: cross gamma_vld_cp, gamma_magnitude_cp;
    endgroup
    
    // Constructor
    function new(string name = "gamma_monitor", uvm_component parent = null);
        super.new(name, parent);
        ap = new("ap", this);
        if (enable_coverage) gamma_cg = new();
    endfunction
    
    // Build phase
    function void build_phase(uvm_phase phase);
        super.build_phase(phase);
        
        // Get virtual interface
        if (!uvm_config_db#(virtual vcom_temp_comp_if)::get(
            this, "", "vif", vif)) begin
            `uvm_fatal("GAMMA_MON", "Virtual interface not found")
        end
        
        // Get configuration
        uvm_config_db#(bit)::get(this, "", "enable_checks", enable_checks);
        uvm_config_db#(bit)::get(this, "", "enable_coverage", enable_coverage);
        uvm_config_db#(bit)::get(this, "", "monitor_all_changes", monitor_all_changes);
    endfunction
    
    // Run phase
    task run_phase(uvm_phase phase);
        fork
            if (monitor_all_changes) monitor_gamma_changes();
            monitor_gamma_vld_events();
            if (enable_checks) monitor_gamma_checks();
        join
    endtask
    
    // Monitor all gamma changes
    task monitor_gamma_changes();
        gamma_result_transaction txn;
        
        // Wait for reset
        @(posedge vif.rst_n);
        repeat(2) @(vif.monitor_cb);
        
        // Initialize tracking
        prev_gamma_tcomp = vif.monitor_cb.gamma_tcomp;
        gamma_samples = 1;
        
        forever begin
            @(vif.monitor_cb);
            
            // Check for gamma change
            if (vif.monitor_cb.gamma_tcomp != prev_gamma_tcomp) begin
                // Create transaction
                txn = gamma_result_transaction::type_id::create("gamma_change_txn");
                txn.timestamp = $time;
                txn.gamma_tcomp = vif.monitor_cb.gamma_tcomp;
                txn.gamma_tcomp_vld = vif.monitor_cb.gamma_tcomp_vld;
                txn.temp_current = vif.monitor_cb.temp;
                txn.temp_ref = vif.monitor_cb.temp_vcom;
                txn.gamma_coeff = vif.monitor_cb.gamma_tcomp_coeff;
                txn.valid = 1;
                
                // Decode and update statistics
                txn.decode_gamma();
                update_gamma_stats(txn.gamma_real);
                
                total_gamma_updates++;
                
                if (txn.gamma_real > 0.001) positive_compensations++;
                else if (txn.gamma_real < -0.001) negative_compensations++;
                else zero_compensations++;
                
                `uvm_info("GAMMA_MON", 
                         $sformatf("Gamma changed: 0x%04h -> 0x%04h (%.4f -> %.4f)", 
                                  prev_gamma_tcomp, vif.monitor_cb.gamma_tcomp,
                                  decode_gamma_value(prev_gamma_tcomp), txn.gamma_real), 
                         UVM_HIGH)
                
                // Send to analysis port
                ap.write(txn);
                
                prev_gamma_tcomp = vif.monitor_cb.gamma_tcomp;
            end
            
            // Sample coverage
            if (enable_coverage) gamma_cg.sample();
        end
    endtask
    
    // Monitor gamma valid events
    task monitor_gamma_vld_events();
        gamma_result_transaction txn;
        
        // Wait for reset
        @(posedge vif.rst_n);
        repeat(2) @(vif.monitor_cb);
        
        forever begin
            // Wait for gamma_tcomp_vld assertion
            @(posedge vif.monitor_cb.gamma_tcomp_vld);
            
            // Create transaction
            txn = gamma_result_transaction::type_id::create("gamma_vld_txn");
            txn.timestamp = $time;
            txn.gamma_tcomp = vif.monitor_cb.gamma_tcomp;
            txn.gamma_tcomp_vld = 1;
            txn.temp_current = vif.monitor_cb.temp;
            txn.temp_ref = vif.monitor_cb.temp_vcom;
            txn.gamma_coeff = vif.monitor_cb.gamma_tcomp_coeff;
            txn.valid = 1;
            
            // Decode gamma value
            txn.decode_gamma();
            
            gamma_vld_events++;
            
            `uvm_info("GAMMA_MON", 
                     $sformatf("Gamma valid event:\n%s", txn.convert2string()), 
                     UVM_MEDIUM)
            
            // Send to analysis port
            ap.write(txn);
        end
    endtask
    
    // Monitor gamma-specific checks
    task monitor_gamma_checks();
        forever begin
            @(vif.monitor_cb);
            
            // Check for gamma saturation
            if (vif.monitor_cb.gamma_tcomp[12:0] == 13'h1FFF) begin
                `uvm_info("GAMMA_MON", 
                         $sformatf("Gamma magnitude saturation detected: 0x%04h", 
                                  vif.monitor_cb.gamma_tcomp), 
                         UVM_MEDIUM)
            end
            
            // Check for gamma_vld timing
            if (vif.monitor_cb.gamma_tcomp_vld) begin
                // gamma_vld should be short pulse
                int vld_duration = 0;
                while (vif.monitor_cb.gamma_tcomp_vld) begin
                    @(vif.monitor_cb);
                    vld_duration++;
                    if (vld_duration > 10) begin
                        `uvm_warning("GAMMA_MON", 
                                    $sformatf("gamma_tcomp_vld held for %0d cycles", vld_duration))
                        break;
                    end
                end
            end
        end
    endtask
    
    // Helper function to decode gamma value
    function real decode_gamma_value(bit [13:0] gamma_val);
        bit sign = gamma_val[13];
        bit [12:0] magnitude = gamma_val[12:0];
        real result = real'(magnitude) / 32.0;
        return sign ? -result : result;
    endfunction
    
    // Update gamma statistics
    function void update_gamma_stats(real gamma_val);
        gamma_samples++;
        avg_gamma = ((avg_gamma * real'(gamma_samples - 1)) + gamma_val) / real'(gamma_samples);
        
        if (gamma_val < min_gamma) min_gamma = gamma_val;
        if (gamma_val > max_gamma) max_gamma = gamma_val;
    endfunction
    
    // Check phase
    function void check_phase(uvm_phase phase);
        if (total_gamma_updates == 0) begin
            `uvm_warning("GAMMA_MON", "No gamma updates detected during simulation")
        end
        
        if (gamma_vld_events == 0) begin
            `uvm_warning("GAMMA_MON", "No gamma_tcomp_vld events detected during simulation")
        end
    endfunction
    
    // Report phase
    function void report_phase(uvm_phase phase);
        `uvm_info("GAMMA_MON", "=== Gamma Monitor Statistics ===", UVM_LOW)
        `uvm_info("GAMMA_MON", $sformatf("Total gamma updates: %0d", total_gamma_updates), UVM_LOW)
        `uvm_info("GAMMA_MON", $sformatf("Gamma valid events: %0d", gamma_vld_events), UVM_LOW)
        `uvm_info("GAMMA_MON", $sformatf("Positive compensations: %0d", positive_compensations), UVM_LOW)
        `uvm_info("GAMMA_MON", $sformatf("Negative compensations: %0d", negative_compensations), UVM_LOW)
        `uvm_info("GAMMA_MON", $sformatf("Zero compensations: %0d", zero_compensations), UVM_LOW)
        
        if (gamma_samples > 0) begin
            `uvm_info("GAMMA_MON", $sformatf("Gamma range: %.4f to %.4f", min_gamma, max_gamma), UVM_LOW)
            `uvm_info("GAMMA_MON", $sformatf("Average gamma: %.4f", avg_gamma), UVM_LOW)
        end
        
        if (enable_coverage) begin
            `uvm_info("GAMMA_MON", 
                     $sformatf("Gamma coverage: %.2f%%", gamma_cg.get_coverage()), 
                     UVM_LOW)
        end
    endfunction
    
endclass : gamma_monitor
