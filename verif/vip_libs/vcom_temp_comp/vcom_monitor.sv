//==============================================================================
// File: vcom_monitor.sv
// Description: VCOM output monitor for temperature compensation verification
//==============================================================================

class vcom_result_transaction extends uvm_sequence_item;
    
    // VCOM compensation result
    bit [7:0] vcom_tcomp;
    
    // Input conditions that led to this result
    bit [7:0] vcom_base;
    bit [7:0] temp_current;
    bit [7:0] temp_ref;
    bit [7:0] vcom_coeff;
    bit       vcom_enable;
    
    // Timing information
    time      timestamp;
    bit       valid;
    
    `uvm_object_utils_begin(vcom_result_transaction)
        `uvm_field_int(vcom_tcomp, UVM_ALL_ON)
        `uvm_field_int(vcom_base, UVM_ALL_ON)
        `uvm_field_int(temp_current, UVM_ALL_ON)
        `uvm_field_int(temp_ref, UVM_ALL_ON)
        `uvm_field_int(vcom_coeff, UVM_ALL_ON)
        `uvm_field_int(vcom_enable, UVM_ALL_ON)
        `uvm_field_int(timestamp, UVM_ALL_ON | UVM_TIME)
        `uvm_field_int(valid, UVM_ALL_ON)
    `uvm_object_utils_end
    
    function new(string name = "vcom_result_transaction");
        super.new(name);
    endfunction
    
    function string convert2string();
        string s;
        s = $sformatf("VCOM Result Transaction:\n");
        s = {s, $sformatf("  vcom_tcomp = 0x%02h (%0d)\n", vcom_tcomp, vcom_tcomp)};
        s = {s, $sformatf("  vcom_base = 0x%02h (%0d)\n", vcom_base, vcom_base)};
        s = {s, $sformatf("  temp_current = %0d°C\n", temp_current)};
        s = {s, $sformatf("  temp_ref = %0d°C\n", temp_ref)};
        s = {s, $sformatf("  vcom_coeff = 0x%02h (%.4f)\n", vcom_coeff, real'(vcom_coeff)/128.0)};
        s = {s, $sformatf("  vcom_enable = %0b\n", vcom_enable)};
        s = {s, $sformatf("  timestamp = %0t\n", timestamp)};
        s = {s, $sformatf("  valid = %0b\n", valid)};
        return s;
    endfunction
    
endclass : vcom_result_transaction

//==============================================================================
// VCOM Monitor Class
//==============================================================================

class vcom_monitor extends uvm_monitor;
    
    `uvm_component_utils(vcom_monitor)
    
    // Virtual interface
    virtual vcom_temp_comp_if.monitor_mp vif;
    
    // Analysis port
    uvm_analysis_port #(vcom_result_transaction) ap;
    
    // Configuration
    bit enable_checks = 1;
    bit enable_coverage = 1;
    bit monitor_all_changes = 1;  // Monitor all VCOM changes, not just valid ones
    
    // Statistics
    int total_vcom_updates = 0;
    int compensation_events = 0;
    int bypass_events = 0;
    
    // VCOM tracking
    bit [7:0] prev_vcom_tcomp = 0;
    bit [7:0] min_vcom = 255;
    bit [7:0] max_vcom = 0;
    real avg_vcom = 0;
    int vcom_samples = 0;
    
    // Coverage
    covergroup vcom_cg;
        option.per_instance = 1;
        
        vcom_tcomp_cp: coverpoint vif.monitor_cb.vcom_tcomp {
            bins very_low[] = {[0:50]};
            bins low[] = {[51:100]};
            bins medium[] = {[101:150]};
            bins high[] = {[151:200]};
            bins very_high[] = {[201:255]};
        }
        
        vcom_base_cp: coverpoint vif.monitor_cb.vcom {
            bins very_low[] = {[0:50]};
            bins low[] = {[51:100]};
            bins medium[] = {[101:150]};
            bins high[] = {[151:200]};
            bins very_high[] = {[201:255]};
        }
        
        temp_cp: coverpoint vif.monitor_cb.temp {
            bins very_cold[] = {[0:10]};
            bins cold[] = {[11:25]};
            bins normal[] = {[26:40]};
            bins warm[] = {[41:60]};
            bins hot[] = {[61:255]};
        }
        
        enable_cp: coverpoint vif.monitor_cb.vcom_tcomp_en {
            bins disabled = {0};
            bins enabled = {1};
        }
        
        coeff_cp: coverpoint vif.monitor_cb.vcom_tcomp_coeff {
            bins small[] = {[1:50]};
            bins medium[] = {[51:150]};
            bins large[] = {[151:255]};
        }
        
        // VCOM change patterns
        vcom_change_cp: coverpoint (vif.monitor_cb.vcom_tcomp - prev_vcom_tcomp) {
            bins no_change = {0};
            bins small_inc[] = {[1:5]};
            bins medium_inc[] = {[6:15]};
            bins large_inc[] = {[16:50]};
            bins huge_inc[] = {[51:255]};
            bins small_dec[] = {[-5:-1]};
            bins medium_dec[] = {[-15:-6]};
            bins large_dec[] = {[-50:-16]};
            bins huge_dec[] = {[-255:-51]};
        }
        
        // Cross coverage
        enable_coeff_cross: cross enable_cp, coeff_cp;
        temp_enable_cross: cross temp_cp, enable_cp;
        vcom_change_enable_cross: cross vcom_change_cp, enable_cp;
    endgroup
    
    // Constructor
    function new(string name = "vcom_monitor", uvm_component parent = null);
        super.new(name, parent);
        ap = new("ap", this);
        if (enable_coverage) vcom_cg = new();
    endfunction
    
    // Build phase
    function void build_phase(uvm_phase phase);
        super.build_phase(phase);
        
        // Get virtual interface
        if (!uvm_config_db#(virtual vcom_temp_comp_if)::get(
            this, "", "vif", vif)) begin
            `uvm_fatal("VCOM_MON", "Virtual interface not found")
        end
        
        // Get configuration
        uvm_config_db#(bit)::get(this, "", "enable_checks", enable_checks);
        uvm_config_db#(bit)::get(this, "", "enable_coverage", enable_coverage);
        uvm_config_db#(bit)::get(this, "", "monitor_all_changes", monitor_all_changes);
    endfunction
    
    // Run phase
    task run_phase(uvm_phase phase);
        fork
            if (monitor_all_changes) monitor_vcom_changes();
            monitor_compensation_events();
            if (enable_checks) monitor_vcom_checks();
        join
    endtask
    
    // Monitor all VCOM changes
    task monitor_vcom_changes();
        vcom_result_transaction txn;
        
        // Wait for reset
        @(posedge vif.rst_n);
        repeat(2) @(vif.monitor_cb);
        
        // Initialize tracking
        prev_vcom_tcomp = vif.monitor_cb.vcom_tcomp;
        min_vcom = prev_vcom_tcomp;
        max_vcom = prev_vcom_tcomp;
        avg_vcom = real'(prev_vcom_tcomp);
        vcom_samples = 1;
        
        forever begin
            @(vif.monitor_cb);
            
            // Update statistics
            vcom_samples++;
            avg_vcom = ((avg_vcom * real'(vcom_samples - 1)) + real'(vif.monitor_cb.vcom_tcomp)) / real'(vcom_samples);
            
            if (vif.monitor_cb.vcom_tcomp < min_vcom) min_vcom = vif.monitor_cb.vcom_tcomp;
            if (vif.monitor_cb.vcom_tcomp > max_vcom) max_vcom = vif.monitor_cb.vcom_tcomp;
            
            // Check for VCOM change
            if (vif.monitor_cb.vcom_tcomp != prev_vcom_tcomp) begin
                // Create transaction
                txn = vcom_result_transaction::type_id::create("vcom_change_txn");
                txn.timestamp = $time;
                txn.vcom_tcomp = vif.monitor_cb.vcom_tcomp;
                txn.vcom_base = vif.monitor_cb.vcom;
                txn.temp_current = vif.monitor_cb.temp;
                txn.temp_ref = vif.monitor_cb.temp_vcom;
                txn.vcom_coeff = vif.monitor_cb.vcom_tcomp_coeff;
                txn.vcom_enable = vif.monitor_cb.vcom_tcomp_en;
                txn.valid = 1;
                
                total_vcom_updates++;
                
                if (txn.vcom_enable) begin
                    compensation_events++;
                end else begin
                    bypass_events++;
                end
                
                `uvm_info("VCOM_MON", 
                         $sformatf("VCOM changed: 0x%02h -> 0x%02h (Δ%+d) %s", 
                                  prev_vcom_tcomp, vif.monitor_cb.vcom_tcomp,
                                  int'(vif.monitor_cb.vcom_tcomp) - int'(prev_vcom_tcomp),
                                  txn.vcom_enable ? "[COMPENSATED]" : "[BYPASS]"), 
                         UVM_HIGH)
                
                // Send to analysis port
                ap.write(txn);
                
                prev_vcom_tcomp = vif.monitor_cb.vcom_tcomp;
            end
            
            // Sample coverage
            if (enable_coverage) vcom_cg.sample();
        end
    endtask
    
    // Monitor compensation events (triggered by temp_vld or timer)
    task monitor_compensation_events();
        vcom_result_transaction txn;
        
        // Wait for reset
        @(posedge vif.rst_n);
        repeat(2) @(vif.monitor_cb);
        
        forever begin
            // Wait for trigger events
            @(posedge vif.monitor_cb.temp_vld or posedge vif.monitor_cb.timer_1s_vld);
            
            // Wait a few cycles for compensation to settle
            repeat(3) @(vif.monitor_cb);
            
            // Create transaction
            txn = vcom_result_transaction::type_id::create("vcom_comp_txn");
            txn.timestamp = $time;
            txn.vcom_tcomp = vif.monitor_cb.vcom_tcomp;
            txn.vcom_base = vif.monitor_cb.vcom;
            txn.temp_current = vif.monitor_cb.temp;
            txn.temp_ref = vif.monitor_cb.temp_vcom;
            txn.vcom_coeff = vif.monitor_cb.vcom_tcomp_coeff;
            txn.vcom_enable = vif.monitor_cb.vcom_tcomp_en;
            txn.valid = 1;
            
            `uvm_info("VCOM_MON", 
                     $sformatf("Compensation event detected:\n%s", txn.convert2string()), 
                     UVM_MEDIUM)
            
            // Send to analysis port
            ap.write(txn);
        end
    endtask
    
    // Monitor VCOM-specific checks
    task monitor_vcom_checks();
        forever begin
            @(vif.monitor_cb);
            
            // Check for VCOM saturation
            if (vif.monitor_cb.vcom_tcomp == 8'h00 || vif.monitor_cb.vcom_tcomp == 8'hFF) begin
                `uvm_info("VCOM_MON", 
                         $sformatf("VCOM saturation detected: 0x%02h", vif.monitor_cb.vcom_tcomp), 
                         UVM_MEDIUM)
            end
            
            // Check for unreasonable VCOM values when compensation is disabled
            if (!vif.monitor_cb.vcom_tcomp_en && 
                (vif.monitor_cb.vcom_tcomp != vif.monitor_cb.vcom)) begin
                `uvm_warning("VCOM_MON", 
                            $sformatf("VCOM mismatch when disabled: tcomp=0x%02h, base=0x%02h", 
                                     vif.monitor_cb.vcom_tcomp, vif.monitor_cb.vcom))
            end
        end
    endtask
    
    // Check phase
    function void check_phase(uvm_phase phase);
        if (total_vcom_updates == 0) begin
            `uvm_warning("VCOM_MON", "No VCOM updates detected during simulation")
        end
    endfunction
    
    // Report phase
    function void report_phase(uvm_phase phase);
        `uvm_info("VCOM_MON", "=== VCOM Monitor Statistics ===", UVM_LOW)
        `uvm_info("VCOM_MON", $sformatf("Total VCOM updates: %0d", total_vcom_updates), UVM_LOW)
        `uvm_info("VCOM_MON", $sformatf("Compensation events: %0d", compensation_events), UVM_LOW)
        `uvm_info("VCOM_MON", $sformatf("Bypass events: %0d", bypass_events), UVM_LOW)
        
        if (vcom_samples > 0) begin
            `uvm_info("VCOM_MON", $sformatf("VCOM range: 0x%02h - 0x%02h (%0d - %0d)", 
                                           min_vcom, max_vcom, min_vcom, max_vcom), UVM_LOW)
            `uvm_info("VCOM_MON", $sformatf("Average VCOM: %.1f (0x%02h)", 
                                           avg_vcom, int'(avg_vcom)), UVM_LOW)
        end
        
        if (enable_coverage) begin
            `uvm_info("VCOM_MON", 
                     $sformatf("VCOM coverage: %.2f%%", vcom_cg.get_coverage()), 
                     UVM_LOW)
        end
    endfunction
    
endclass : vcom_monitor
