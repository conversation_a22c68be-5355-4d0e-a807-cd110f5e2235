//==============================================================================
// File: config_transaction.sv
// Description: Configuration transaction for I2C-like configuration protocol
//==============================================================================

class config_transaction extends uvm_sequence_item;
    
    // Configuration parameters
    rand bit [7:0] vcom;
    rand bit [7:0] vcom_tcomp_coeff;   // (u,1,7) format
    rand bit [7:0] gamma_tcomp_coeff;  // (u,1,7) format
    rand bit [7:0] temp_vcom;          // Reference temperature
    rand bit [7:0] vcom_tcomp_th;      // Threshold
    rand bit       vcom_tcomp_en;      // Enable compensation
    rand bit       vcom_tcomp_mode;    // 0: temp, 1: timer
    
    // Timing control
    rand int       config_delay;       // Delay before configuration
    rand int       para_wr_cycles;     // Duration of para_wr pulse
    rand bit       batch_config;       // Configure all parameters at once
    
    // Transaction metadata
    time           start_time;
    time           end_time;
    bit            completed;
    
    `uvm_object_utils_begin(config_transaction)
        `uvm_field_int(vcom, UVM_ALL_ON)
        `uvm_field_int(vcom_tcomp_coeff, UVM_ALL_ON)
        `uvm_field_int(gamma_tcomp_coeff, UVM_ALL_ON)
        `uvm_field_int(temp_vcom, UVM_ALL_ON)
        `uvm_field_int(vcom_tcomp_th, UVM_ALL_ON)
        `uvm_field_int(vcom_tcomp_en, UVM_ALL_ON)
        `uvm_field_int(vcom_tcomp_mode, UVM_ALL_ON)
        `uvm_field_int(config_delay, UVM_ALL_ON)
        `uvm_field_int(para_wr_cycles, UVM_ALL_ON)
        `uvm_field_int(batch_config, UVM_ALL_ON)
        `uvm_field_int(start_time, UVM_ALL_ON | UVM_TIME)
        `uvm_field_int(end_time, UVM_ALL_ON | UVM_TIME)
        `uvm_field_int(completed, UVM_ALL_ON)
    `uvm_object_utils_end
    
    // Constraints
    
    // Valid coefficient range (u,1,7 format: 0.0078125 to 1.9921875)
    constraint valid_coeff_c {
        vcom_tcomp_coeff inside {[1:255]};
        gamma_tcomp_coeff inside {[1:255]};
    }
    
    // Reasonable VCOM values
    constraint reasonable_vcom_c {
        vcom inside {[50:200]};  // Typical VCOM range
    }
    
    // Reasonable reference temperature
    constraint reasonable_temp_vcom_c {
        temp_vcom inside {[15:35]};  // 15°C to 35°C
    }
    
    // Threshold values
    constraint reasonable_threshold_c {
        vcom_tcomp_th inside {[1:10]};  // 1°C to 10°C or 1s to 10s
    }
    
    // Timing constraints
    constraint timing_c {
        config_delay inside {[0:20]};     // 0 to 20 clk_rg cycles
        para_wr_cycles inside {[1:5]};    // 1 to 5 clk_rg cycles
        batch_config dist {1 := 70, 0 := 30};  // Prefer batch config
    }
    
    // Constructor
    function new(string name = "config_transaction");
        super.new(name);
    endfunction
    
    // Convert coefficient to real value
    function real coeff_to_real(bit [7:0] coeff_val);
        return real'(coeff_val) / 128.0;  // Divide by 2^7
    endfunction
    
    // Validation function
    function bit is_valid();
        if (vcom_tcomp_coeff == 0 || gamma_tcomp_coeff == 0) begin
            `uvm_error("CONFIG_TXN", "Coefficients cannot be zero")
            return 0;
        end
        if (para_wr_cycles == 0) begin
            `uvm_error("CONFIG_TXN", "para_wr_cycles cannot be zero")
            return 0;
        end
        return 1;
    endfunction
    
    // Pretty print function
    function string convert2string();
        string s;
        s = $sformatf("Config Transaction:\n");
        s = {s, $sformatf("  vcom = 0x%02h (%0d)\n", vcom, vcom)};
        s = {s, $sformatf("  vcom_tcomp_coeff = 0x%02h (%.4f)\n", 
                         vcom_tcomp_coeff, coeff_to_real(vcom_tcomp_coeff))};
        s = {s, $sformatf("  gamma_tcomp_coeff = 0x%02h (%.4f)\n", 
                         gamma_tcomp_coeff, coeff_to_real(gamma_tcomp_coeff))};
        s = {s, $sformatf("  temp_vcom = %0d°C\n", temp_vcom)};
        s = {s, $sformatf("  vcom_tcomp_th = %0d\n", vcom_tcomp_th)};
        s = {s, $sformatf("  vcom_tcomp_en = %0b\n", vcom_tcomp_en)};
        s = {s, $sformatf("  vcom_tcomp_mode = %0b (%s)\n", 
                         vcom_tcomp_mode, vcom_tcomp_mode ? "timer" : "temp")};
        s = {s, $sformatf("  config_delay = %0d cycles\n", config_delay)};
        s = {s, $sformatf("  para_wr_cycles = %0d cycles\n", para_wr_cycles)};
        s = {s, $sformatf("  batch_config = %0b\n", batch_config)};
        if (completed) begin
            s = {s, $sformatf("  Duration = %0t\n", end_time - start_time)};
        end
        return s;
    endfunction
    
    // Copy function
    function void do_copy(uvm_object rhs);
        config_transaction rhs_;
        if (!$cast(rhs_, rhs)) begin
            `uvm_fatal("CONFIG_TXN", "Cast failed in do_copy")
        end
        super.do_copy(rhs);
        vcom = rhs_.vcom;
        vcom_tcomp_coeff = rhs_.vcom_tcomp_coeff;
        gamma_tcomp_coeff = rhs_.gamma_tcomp_coeff;
        temp_vcom = rhs_.temp_vcom;
        vcom_tcomp_th = rhs_.vcom_tcomp_th;
        vcom_tcomp_en = rhs_.vcom_tcomp_en;
        vcom_tcomp_mode = rhs_.vcom_tcomp_mode;
        config_delay = rhs_.config_delay;
        para_wr_cycles = rhs_.para_wr_cycles;
        batch_config = rhs_.batch_config;
        start_time = rhs_.start_time;
        end_time = rhs_.end_time;
        completed = rhs_.completed;
    endfunction
    
    // Compare function
    function bit do_compare(uvm_object rhs, uvm_comparer comparer);
        config_transaction rhs_;
        if (!$cast(rhs_, rhs)) return 0;
        
        return (super.do_compare(rhs, comparer) &&
                (vcom == rhs_.vcom) &&
                (vcom_tcomp_coeff == rhs_.vcom_tcomp_coeff) &&
                (gamma_tcomp_coeff == rhs_.gamma_tcomp_coeff) &&
                (temp_vcom == rhs_.temp_vcom) &&
                (vcom_tcomp_th == rhs_.vcom_tcomp_th) &&
                (vcom_tcomp_en == rhs_.vcom_tcomp_en) &&
                (vcom_tcomp_mode == rhs_.vcom_tcomp_mode));
    endfunction
    
endclass : config_transaction
