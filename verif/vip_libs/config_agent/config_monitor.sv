//==============================================================================
// File: config_monitor.sv
// Description: Configuration monitor for I2C-like configuration protocol
//==============================================================================

class config_monitor extends uvm_monitor;
    
    `uvm_component_utils(config_monitor)
    
    // Virtual interface
    virtual vcom_temp_comp_if.monitor_mp vif;
    
    // Analysis port
    uvm_analysis_port #(config_transaction) ap;
    
    // Configuration
    bit enable_checks = 1;
    bit enable_coverage = 1;
    
    // Statistics
    int total_configs = 0;
    int protocol_violations = 0;
    
    // Coverage
    covergroup config_cg;
        option.per_instance = 1;
        
        vcom_cp: coverpoint vif.monitor_cb.vcom {
            bins low_vcom[] = {[0:85]};
            bins mid_vcom[] = {[86:170]};
            bins high_vcom[] = {[171:255]};
        }
        
        vcom_coeff_cp: coverpoint vif.monitor_cb.vcom_tcomp_coeff {
            bins small_coeff[] = {[1:50]};
            bins medium_coeff[] = {[51:150]};
            bins large_coeff[] = {[151:255]};
        }
        
        gamma_coeff_cp: coverpoint vif.monitor_cb.gamma_tcomp_coeff {
            bins small_coeff[] = {[1:50]};
            bins medium_coeff[] = {[51:150]};
            bins large_coeff[] = {[151:255]};
        }
        
        temp_vcom_cp: coverpoint vif.monitor_cb.temp_vcom {
            bins cold[] = {[0:15]};
            bins normal[] = {[16:30]};
            bins warm[] = {[31:50]};
            bins hot[] = {[51:255]};
        }
        
        enable_cp: coverpoint vif.monitor_cb.vcom_tcomp_en {
            bins disabled = {0};
            bins enabled = {1};
        }
        
        mode_cp: coverpoint vif.monitor_cb.vcom_tcomp_mode {
            bins temp_mode = {0};
            bins timer_mode = {1};
        }
        
        threshold_cp: coverpoint vif.monitor_cb.vcom_tcomp_th {
            bins low_th[] = {[1:3]};
            bins med_th[] = {[4:7]};
            bins high_th[] = {[8:255]};
        }
        
        para_wr_cp: coverpoint vif.monitor_cb.para_wr {
            bins inactive = {0};
            bins active = {1};
        }
        
        // Cross coverage
        enable_mode_cross: cross enable_cp, mode_cp;
        coeff_cross: cross vcom_coeff_cp, gamma_coeff_cp;
        config_cross: cross para_wr_cp, enable_cp;
    endgroup
    
    // Constructor
    function new(string name = "config_monitor", uvm_component parent = null);
        super.new(name, parent);
        ap = new("ap", this);
        if (enable_coverage) config_cg = new();
    endfunction
    
    // Build phase
    function void build_phase(uvm_phase phase);
        super.build_phase(phase);
        
        // Get virtual interface
        if (!uvm_config_db#(virtual vcom_temp_comp_if)::get(
            this, "", "vif", vif)) begin
            `uvm_fatal("CONFIG_MON", "Virtual interface not found")
        end
        
        // Get configuration
        uvm_config_db#(bit)::get(this, "", "enable_checks", enable_checks);
        uvm_config_db#(bit)::get(this, "", "enable_coverage", enable_coverage);
    endfunction
    
    // Run phase
    task run_phase(uvm_phase phase);
        fork
            monitor_config_transactions();
            if (enable_checks) monitor_protocol_violations();
        join
    endtask
    
    // Monitor configuration transactions
    task monitor_config_transactions();
        config_transaction txn;
        bit [7:0] prev_vcom, prev_vcom_coeff, prev_gamma_coeff, prev_temp_vcom;
        bit [7:0] prev_threshold;
        bit prev_enable, prev_mode;
        
        // Wait for reset
        @(posedge vif.rst_rg_n);
        repeat(2) @(vif.monitor_cb);
        
        // Initialize previous values
        prev_vcom = vif.monitor_cb.vcom;
        prev_vcom_coeff = vif.monitor_cb.vcom_tcomp_coeff;
        prev_gamma_coeff = vif.monitor_cb.gamma_tcomp_coeff;
        prev_temp_vcom = vif.monitor_cb.temp_vcom;
        prev_threshold = vif.monitor_cb.vcom_tcomp_th;
        prev_enable = vif.monitor_cb.vcom_tcomp_en;
        prev_mode = vif.monitor_cb.vcom_tcomp_mode;
        
        forever begin
            // Wait for para_wr assertion
            @(posedge vif.monitor_cb.para_wr);
            
            // Create transaction
            txn = config_transaction::type_id::create("config_txn");
            txn.start_time = $time;
            
            // Wait for para_wr deassertion
            @(negedge vif.monitor_cb.para_wr);
            txn.end_time = $time;
            
            // Capture configuration values
            txn.vcom = vif.monitor_cb.vcom;
            txn.vcom_tcomp_coeff = vif.monitor_cb.vcom_tcomp_coeff;
            txn.gamma_tcomp_coeff = vif.monitor_cb.gamma_tcomp_coeff;
            txn.temp_vcom = vif.monitor_cb.temp_vcom;
            txn.vcom_tcomp_th = vif.monitor_cb.vcom_tcomp_th;
            txn.vcom_tcomp_en = vif.monitor_cb.vcom_tcomp_en;
            txn.vcom_tcomp_mode = vif.monitor_cb.vcom_tcomp_mode;
            txn.completed = 1;
            
            // Determine if this was a batch configuration
            txn.batch_config = (
                (txn.vcom != prev_vcom) +
                (txn.vcom_tcomp_coeff != prev_vcom_coeff) +
                (txn.gamma_tcomp_coeff != prev_gamma_coeff) +
                (txn.temp_vcom != prev_temp_vcom) +
                (txn.vcom_tcomp_th != prev_threshold) +
                (txn.vcom_tcomp_en != prev_enable) +
                (txn.vcom_tcomp_mode != prev_mode)
            ) > 1;
            
            // Update previous values
            prev_vcom = txn.vcom;
            prev_vcom_coeff = txn.vcom_tcomp_coeff;
            prev_gamma_coeff = txn.gamma_tcomp_coeff;
            prev_temp_vcom = txn.temp_vcom;
            prev_threshold = txn.vcom_tcomp_th;
            prev_enable = txn.vcom_tcomp_en;
            prev_mode = txn.vcom_tcomp_mode;
            
            total_configs++;
            
            `uvm_info("CONFIG_MON", 
                     $sformatf("Configuration transaction detected:\n%s", txn.convert2string()), 
                     UVM_MEDIUM)
            
            // Send to analysis port
            ap.write(txn);
            
            // Sample coverage
            if (enable_coverage) config_cg.sample();
        end
    endtask
    
    // Monitor protocol violations
    task monitor_protocol_violations();
        int para_wr_duration;
        
        forever begin
            @(posedge vif.monitor_cb.para_wr);
            
            // Check para_wr duration
            para_wr_duration = 0;
            while (vif.monitor_cb.para_wr) begin
                @(vif.monitor_cb);
                para_wr_duration++;
                
                // Check for excessive para_wr duration
                if (para_wr_duration > 10) begin
                    `uvm_warning("CONFIG_MON", 
                                $sformatf("para_wr held for %0d cycles - potential violation", 
                                         para_wr_duration))
                    protocol_violations++;
                    break;
                end
            end
            
            // Check minimum para_wr duration
            if (para_wr_duration == 0) begin
                `uvm_error("CONFIG_MON", "para_wr pulse too short")
                protocol_violations++;
            end
        end
    endtask
    
    // Check phase
    function void check_phase(uvm_phase phase);
        if (enable_checks && protocol_violations > 0) begin
            `uvm_warning("CONFIG_MON", 
                        $sformatf("Detected %0d protocol violations", protocol_violations))
        end
    endfunction
    
    // Report phase
    function void report_phase(uvm_phase phase);
        `uvm_info("CONFIG_MON", "=== Configuration Monitor Statistics ===", UVM_LOW)
        `uvm_info("CONFIG_MON", $sformatf("Total configurations: %0d", total_configs), UVM_LOW)
        `uvm_info("CONFIG_MON", $sformatf("Protocol violations: %0d", protocol_violations), UVM_LOW)
        
        if (enable_coverage) begin
            `uvm_info("CONFIG_MON", 
                     $sformatf("Configuration coverage: %.2f%%", config_cg.get_coverage()), 
                     UVM_LOW)
        end
    endfunction
    
endclass : config_monitor
