//==============================================================================
// File: config_agent_pkg.sv
// Description: Package for configuration agent
//==============================================================================

package config_agent_pkg;
    
    import uvm_pkg::*;
    `include "uvm_macros.svh"
    
    // Import common package
    import vcom_temp_comp_pkg::*;
    
    // Include agent files
    `include "config_transaction.sv"
    `include "config_sequencer.sv"
    `include "config_driver.sv"
    `include "config_monitor.sv"
    `include "config_agent.sv"
    
endpackage : config_agent_pkg
