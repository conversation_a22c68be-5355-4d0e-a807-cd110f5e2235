//==============================================================================
// File: config_driver.sv
// Description: Configuration driver for I2C-like configuration protocol
//==============================================================================

class config_driver extends uvm_driver #(config_transaction);
    
    `uvm_component_utils(config_driver)
    
    // Virtual interface
    virtual vcom_temp_comp_if.config_mp vif;
    
    // Configuration
    bit enable_protocol_checks = 1;
    int max_config_timeout = 1000;  // Maximum cycles to wait
    
    // Statistics
    int total_configs = 0;
    int failed_configs = 0;
    time total_config_time = 0;
    
    // Constructor
    function new(string name = "config_driver", uvm_component parent = null);
        super.new(name, parent);
    endfunction
    
    // Build phase
    function void build_phase(uvm_phase phase);
        super.build_phase(phase);
        
        // Get virtual interface
        if (!uvm_config_db#(virtual vcom_temp_comp_if)::get(
            this, "", "vif", vif)) begin
            `uvm_fatal("CONFIG_DRV", "Virtual interface not found")
        end
        
        // Get configuration parameters
        uvm_config_db#(bit)::get(this, "", "enable_protocol_checks", 
                                enable_protocol_checks);
        uvm_config_db#(int)::get(this, "", "max_config_timeout", 
                                max_config_timeout);
    endfunction
    
    // Run phase
    task run_phase(uvm_phase phase);
        config_transaction req;
        
        // Initialize interface
        initialize_interface();
        
        // Wait for reset deassertion
        wait_for_reset();
        
        forever begin
            // Get next transaction
            seq_item_port.get_next_item(req);
            
            `uvm_info("CONFIG_DRV", 
                     $sformatf("Driving config transaction:\n%s", req.convert2string()), 
                     UVM_MEDIUM)
            
            // Drive the transaction
            drive_transaction(req);
            
            // Mark transaction as done
            seq_item_port.item_done();
        end
    endtask
    
    // Initialize interface signals
    task initialize_interface();
        vif.config_cb.para_wr <= 1'b0;
        vif.config_cb.vcom <= 8'h80;  // Default VCOM
        vif.config_cb.vcom_tcomp_en <= 1'b0;
        vif.config_cb.vcom_tcomp_mode <= 1'b0;
        vif.config_cb.vcom_tcomp_th <= 8'h05;
        vif.config_cb.vcom_tcomp_coeff <= 8'h40;  // Default coefficient
        vif.config_cb.temp_vcom <= 8'h19;  // 25°C
        vif.config_cb.gamma_tcomp_coeff <= 8'h40;
        
        `uvm_info("CONFIG_DRV", "Interface initialized", UVM_HIGH)
    endtask
    
    // Wait for reset deassertion
    task wait_for_reset();
        @(posedge vif.rst_rg_n);
        repeat(2) @(vif.config_cb);  // Wait a few cycles after reset
        `uvm_info("CONFIG_DRV", "Reset deassertion detected", UVM_HIGH)
    endtask
    
    // Drive a configuration transaction
    task drive_transaction(config_transaction req);
        time start_time, end_time;
        
        start_time = $time;
        req.start_time = start_time;
        
        // Pre-configuration delay
        if (req.config_delay > 0) begin
            repeat(req.config_delay) @(vif.config_cb);
        end
        
        if (req.batch_config) begin
            // Configure all parameters at once
            drive_batch_config(req);
        end else begin
            // Configure parameters individually
            drive_individual_config(req);
        end
        
        end_time = $time;
        req.end_time = end_time;
        req.completed = 1;
        
        total_configs++;
        total_config_time += (end_time - start_time);
        
        `uvm_info("CONFIG_DRV", 
                 $sformatf("Configuration completed in %0t", end_time - start_time), 
                 UVM_HIGH)
    endtask
    
    // Drive batch configuration (all parameters at once)
    task drive_batch_config(config_transaction req);
        // Set all configuration values
        vif.config_cb.vcom <= req.vcom;
        vif.config_cb.vcom_tcomp_coeff <= req.vcom_tcomp_coeff;
        vif.config_cb.gamma_tcomp_coeff <= req.gamma_tcomp_coeff;
        vif.config_cb.temp_vcom <= req.temp_vcom;
        vif.config_cb.vcom_tcomp_th <= req.vcom_tcomp_th;
        vif.config_cb.vcom_tcomp_en <= req.vcom_tcomp_en;
        vif.config_cb.vcom_tcomp_mode <= req.vcom_tcomp_mode;
        
        @(vif.config_cb);  // Wait one cycle for setup
        
        // Assert para_wr
        drive_para_wr_pulse(req.para_wr_cycles);
        
        `uvm_info("CONFIG_DRV", "Batch configuration driven", UVM_HIGH)
    endtask
    
    // Drive individual configuration (one parameter at a time)
    task drive_individual_config(config_transaction req);
        // Configure VCOM
        vif.config_cb.vcom <= req.vcom;
        @(vif.config_cb);
        drive_para_wr_pulse(req.para_wr_cycles);
        
        // Configure VCOM coefficient
        vif.config_cb.vcom_tcomp_coeff <= req.vcom_tcomp_coeff;
        @(vif.config_cb);
        drive_para_wr_pulse(req.para_wr_cycles);
        
        // Configure Gamma coefficient
        vif.config_cb.gamma_tcomp_coeff <= req.gamma_tcomp_coeff;
        @(vif.config_cb);
        drive_para_wr_pulse(req.para_wr_cycles);
        
        // Configure reference temperature
        vif.config_cb.temp_vcom <= req.temp_vcom;
        @(vif.config_cb);
        drive_para_wr_pulse(req.para_wr_cycles);
        
        // Configure threshold
        vif.config_cb.vcom_tcomp_th <= req.vcom_tcomp_th;
        @(vif.config_cb);
        drive_para_wr_pulse(req.para_wr_cycles);
        
        // Configure enable and mode
        vif.config_cb.vcom_tcomp_en <= req.vcom_tcomp_en;
        vif.config_cb.vcom_tcomp_mode <= req.vcom_tcomp_mode;
        @(vif.config_cb);
        drive_para_wr_pulse(req.para_wr_cycles);
        
        `uvm_info("CONFIG_DRV", "Individual configuration driven", UVM_HIGH)
    endtask
    
    // Drive para_wr pulse
    task drive_para_wr_pulse(int cycles);
        // Assert para_wr
        vif.config_cb.para_wr <= 1'b1;
        
        // Hold for specified cycles
        repeat(cycles) @(vif.config_cb);
        
        // Deassert para_wr
        vif.config_cb.para_wr <= 1'b0;
        
        // Wait one cycle for deassertion
        @(vif.config_cb);
        
        `uvm_info("CONFIG_DRV", 
                 $sformatf("para_wr pulse driven for %0d cycles", cycles), 
                 UVM_DEBUG)
    endtask
    
    // Report phase
    function void report_phase(uvm_phase phase);
        real avg_config_time;
        
        `uvm_info("CONFIG_DRV", "=== Configuration Driver Statistics ===", UVM_LOW)
        `uvm_info("CONFIG_DRV", $sformatf("Total configurations: %0d", total_configs), UVM_LOW)
        `uvm_info("CONFIG_DRV", $sformatf("Failed configurations: %0d", failed_configs), UVM_LOW)
        
        if (total_configs > 0) begin
            avg_config_time = real'(total_config_time) / real'(total_configs);
            `uvm_info("CONFIG_DRV", 
                     $sformatf("Average configuration time: %.2f ns", avg_config_time), 
                     UVM_LOW)
        end
    endfunction
    
endclass : config_driver
