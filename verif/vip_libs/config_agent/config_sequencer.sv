//==============================================================================
// File: config_sequencer.sv
// Description: Configuration sequencer for I2C-like configuration protocol
//==============================================================================

class config_sequencer extends uvm_sequencer #(config_transaction);
    
    `uvm_component_utils(config_sequencer)
    
    // Configuration parameters
    int max_outstanding_reqs = 1;  // Only one config at a time
    bit enable_flow_control = 1;
    
    // Statistics
    int total_requests = 0;
    int completed_requests = 0;
    int dropped_requests = 0;
    
    // Constructor
    function new(string name = "config_sequencer", uvm_component parent = null);
        super.new(name, parent);
    endfunction
    
    // Build phase
    function void build_phase(uvm_phase phase);
        super.build_phase(phase);
        
        // Get configuration
        uvm_config_db#(int)::get(this, "", "max_outstanding_reqs", max_outstanding_reqs);
        uvm_config_db#(bit)::get(this, "", "enable_flow_control", enable_flow_control);
        
        `uvm_info("CONFIG_SEQ", 
                 $sformatf("Sequencer configured: max_outstanding=%0d, flow_control=%0b", 
                          max_outstanding_reqs, enable_flow_control), 
                 UVM_MEDIUM)
    endfunction
    
    // Override get_next_item to implement flow control
    task get_next_item(output REQ t);
        total_requests++;
        
        if (enable_flow_control) begin
            // Check if we can accept more requests
            while (num_reqs_sent - num_rsps_received >= max_outstanding_reqs) begin
                `uvm_info("CONFIG_SEQ", "Flow control: waiting for response", UVM_DEBUG)
                wait_for_item_done();
            end
        end
        
        super.get_next_item(t);
        
        `uvm_info("CONFIG_SEQ", 
                 $sformatf("Dispatching config request #%0d", total_requests), 
                 UVM_HIGH)
    endtask
    
    // Override item_done to track completions
    function void item_done(RSP item = null);
        completed_requests++;
        super.item_done(item);
        
        `uvm_info("CONFIG_SEQ", 
                 $sformatf("Config request completed (%0d/%0d)", 
                          completed_requests, total_requests), 
                 UVM_HIGH)
    endfunction
    
    // Utility function to check if sequencer is idle
    function bit is_idle();
        return (num_reqs_sent == num_rsps_received);
    endfunction
    
    // Utility function to get pending request count
    function int get_pending_count();
        return (num_reqs_sent - num_rsps_received);
    endfunction
    
    // Report phase
    function void report_phase(uvm_phase phase);
        `uvm_info("CONFIG_SEQ", "=== Configuration Sequencer Statistics ===", UVM_LOW)
        `uvm_info("CONFIG_SEQ", $sformatf("Total requests: %0d", total_requests), UVM_LOW)
        `uvm_info("CONFIG_SEQ", $sformatf("Completed requests: %0d", completed_requests), UVM_LOW)
        `uvm_info("CONFIG_SEQ", $sformatf("Dropped requests: %0d", dropped_requests), UVM_LOW)
        
        if (total_requests != completed_requests) begin
            `uvm_warning("CONFIG_SEQ", 
                        $sformatf("Mismatch: %0d requests sent, %0d completed", 
                                 total_requests, completed_requests))
        end
    endfunction
    
endclass : config_sequencer

//==============================================================================
// Configuration sequence base class
//==============================================================================

class config_base_sequence extends uvm_sequence #(config_transaction);
    
    `uvm_object_utils(config_base_sequence)
    
    // Configuration
    int num_transactions = 1;
    bit randomize_timing = 1;
    
    // Constructor
    function new(string name = "config_base_sequence");
        super.new(name);
    endfunction
    
    // Pre-body task for common setup
    task pre_body();
        if (starting_phase != null) begin
            starting_phase.raise_objection(this, "Config sequence starting");
        end
    endtask
    
    // Post-body task for common cleanup
    task post_body();
        if (starting_phase != null) begin
            starting_phase.drop_objection(this, "Config sequence completed");
        end
    endtask
    
    // Body task - to be overridden by derived classes
    virtual task body();
        `uvm_error("CONFIG_BASE_SEQ", "Body task must be implemented in derived class")
    endtask
    
endclass : config_base_sequence

//==============================================================================
// Simple configuration sequence
//==============================================================================

class config_simple_sequence extends config_base_sequence;
    
    `uvm_object_utils(config_simple_sequence)
    
    // Constructor
    function new(string name = "config_simple_sequence");
        super.new(name);
    endfunction
    
    // Body task
    virtual task body();
        config_transaction req;
        
        repeat(num_transactions) begin
            req = config_transaction::type_id::create("config_req");
            
            start_item(req);
            
            if (!req.randomize()) begin
                `uvm_error("CONFIG_SEQ", "Randomization failed")
            end
            
            if (randomize_timing) begin
                // Add some randomization to timing
                if (!req.randomize(config_delay, para_wr_cycles)) begin
                    `uvm_warning("CONFIG_SEQ", "Timing randomization failed")
                end
            end
            
            `uvm_info("CONFIG_SEQ", 
                     $sformatf("Sending config transaction:\n%s", req.convert2string()), 
                     UVM_HIGH)
            
            finish_item(req);
        end
    endtask
    
endclass : config_simple_sequence
