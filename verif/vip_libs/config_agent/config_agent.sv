//==============================================================================
// File: config_agent.sv
// Description: Configuration agent for I2C-like configuration protocol
//==============================================================================

class config_agent extends uvm_agent;
    
    `uvm_component_utils(config_agent)
    
    // Agent components
    config_driver    driver;
    config_monitor   monitor;
    config_sequencer sequencer;
    
    // Analysis port
    uvm_analysis_port #(config_transaction) ap;
    
    // Configuration
    uvm_active_passive_enum is_active = UVM_ACTIVE;
    bit enable_coverage = 1;
    bit enable_checks = 1;
    
    // Constructor
    function new(string name = "config_agent", uvm_component parent = null);
        super.new(name, parent);
    endfunction
    
    // Build phase
    function void build_phase(uvm_phase phase);
        super.build_phase(phase);
        
        // Get configuration
        uvm_config_db#(uvm_active_passive_enum)::get(this, "", "is_active", is_active);
        uvm_config_db#(bit)::get(this, "", "enable_coverage", enable_coverage);
        uvm_config_db#(bit)::get(this, "", "enable_checks", enable_checks);
        
        // Create monitor (always present)
        monitor = config_monitor::type_id::create("monitor", this);
        
        // Set monitor configuration
        uvm_config_db#(bit)::set(this, "monitor", "enable_coverage", enable_coverage);
        uvm_config_db#(bit)::set(this, "monitor", "enable_checks", enable_checks);
        
        // Create active components if agent is active
        if (is_active == UVM_ACTIVE) begin
            driver = config_driver::type_id::create("driver", this);
            sequencer = config_sequencer::type_id::create("sequencer", this);
        end
        
        `uvm_info("CONFIG_AGENT", 
                 $sformatf("Agent created: is_active=%s, coverage=%0b, checks=%0b", 
                          is_active.name(), enable_coverage, enable_checks), 
                 UVM_MEDIUM)
    endfunction
    
    // Connect phase
    function void connect_phase(uvm_phase phase);
        super.connect_phase(phase);
        
        // Connect analysis port
        ap = monitor.ap;
        
        // Connect driver and sequencer if active
        if (is_active == UVM_ACTIVE) begin
            driver.seq_item_port.connect(sequencer.seq_item_export);
        end
        
        `uvm_info("CONFIG_AGENT", "Agent connections completed", UVM_HIGH)
    endfunction
    
    // End of elaboration phase
    function void end_of_elaboration_phase(uvm_phase phase);
        super.end_of_elaboration_phase(phase);
        
        // Print agent configuration
        `uvm_info("CONFIG_AGENT", "=== Configuration Agent Setup ===", UVM_MEDIUM)
        `uvm_info("CONFIG_AGENT", $sformatf("  Active: %s", is_active.name()), UVM_MEDIUM)
        `uvm_info("CONFIG_AGENT", $sformatf("  Coverage: %0b", enable_coverage), UVM_MEDIUM)
        `uvm_info("CONFIG_AGENT", $sformatf("  Checks: %0b", enable_checks), UVM_MEDIUM)
        
        if (is_active == UVM_ACTIVE) begin
            `uvm_info("CONFIG_AGENT", "  Components: Driver, Monitor, Sequencer", UVM_MEDIUM)
        end else begin
            `uvm_info("CONFIG_AGENT", "  Components: Monitor only", UVM_MEDIUM)
        end
    endfunction
    
    // Run phase
    task run_phase(uvm_phase phase);
        // Agent-level monitoring or coordination tasks can go here
        // For now, just wait for the phase to end
        phase.phase_done.wait_for_all();
    endtask
    
    // Report phase
    function void report_phase(uvm_phase phase);
        `uvm_info("CONFIG_AGENT", "=== Configuration Agent Report ===", UVM_LOW)
        
        // Report will be handled by individual components
        // Agent can provide summary information here if needed
    endfunction
    
endclass : config_agent

//==============================================================================
// Configuration agent configuration object
//==============================================================================

class config_agent_config extends uvm_object;
    
    `uvm_object_utils(config_agent_config)
    
    // Configuration parameters
    uvm_active_passive_enum is_active = UVM_ACTIVE;
    bit enable_coverage = 1;
    bit enable_checks = 1;
    bit enable_protocol_checks = 1;
    int max_config_timeout = 1000;
    int max_outstanding_reqs = 1;
    bit enable_flow_control = 1;
    
    // Interface handle
    virtual vcom_temp_comp_if vif;
    
    // Constructor
    function new(string name = "config_agent_config");
        super.new(name);
    endfunction
    
    // Convert to string
    function string convert2string();
        string s;
        s = $sformatf("Config Agent Configuration:\n");
        s = {s, $sformatf("  is_active = %s\n", is_active.name())};
        s = {s, $sformatf("  enable_coverage = %0b\n", enable_coverage)};
        s = {s, $sformatf("  enable_checks = %0b\n", enable_checks)};
        s = {s, $sformatf("  enable_protocol_checks = %0b\n", enable_protocol_checks)};
        s = {s, $sformatf("  max_config_timeout = %0d\n", max_config_timeout)};
        s = {s, $sformatf("  max_outstanding_reqs = %0d\n", max_outstanding_reqs)};
        s = {s, $sformatf("  enable_flow_control = %0b\n", enable_flow_control)};
        return s;
    endfunction
    
endclass : config_agent_config
