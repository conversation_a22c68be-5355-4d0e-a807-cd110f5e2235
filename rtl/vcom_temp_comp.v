module vcom_temp_comp (
    input  wire        clk,
    input  wire        clk_rg,
    input  wire        rst_n,
    input  wire        rst_rg_n,
    input  wire        para_wr,
    input  wire [7:0]  vcom,
    input  wire        vcom_tcomp_en,
    input  wire        vcom_tcomp_mode,    // 0: temp, 1: timer
    input  wire [7:0]  vcom_tcomp_th,      // temp or time threshold
    input  wire [7:0]  vcom_tcomp_coeff,   // (u,1,7)
    input  wire [7:0]  temp_vcom,          // temp corresponds to vcom
    input  wire [7:0]  temp,
    input  wire        temp_vld,
    input  wire        timer_1s_vld,
    input  wire [7:0]  gamma_tcomp_coeff,  // (u,1,7)
    output reg  [13:0] gamma_tcomp,        // (s,9,5)
    output reg         gamma_tcomp_vld,
    output wire [7:0]  vcom_tcomp
);

always @(posedge clk or negedge rst_rg_n) begin
    if (rst_rg_n == 1'b0) begin
        vcom_tcomp_mode_eff   <= 1'b0;
        vcom_tcomp_th_eff     <= 8'd3;
        vcom_tcomp_coeff_eff  <= 8'b0;
        gamma_tcomp_coeff_eff <= 8'b0;  // (u,1,7)
        temp_vcom_eff         <= 8'b0;
    end
    else begin
        if (para_wr_rise == 1'b1) begin
            vcom_tcomp_mode_eff   <= vcom_tcomp_mode;
            vcom_tcomp_th_eff     <= vcom_tcomp_th;
            vcom_tcomp_coeff_eff  <= vcom_tcomp_coeff;
            gamma_tcomp_coeff_eff <= gamma_tcomp_coeff; // (u,1,7)
            temp_vcom_eff         <= temp_vcom;
        end
    end
end

SYNC_PULSE_ATOM #(
    .N_PIPE_DLY ( N_SYNC_PIPE )
)
U_SYNC_PARA_WR_RISE
(
    .clk_src    ( clk_rg     ),
    .clk_dest   ( clk        ),
    .rst_dest_n ( rst_rg_n   ),
    .sig_src    ( para_wr    ),
    .sig_sync   ( para_wr_rise )
);



endmodule