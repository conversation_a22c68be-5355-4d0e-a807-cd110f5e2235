核心原理总结

根据原理图，系统补偿的核心思想是基于一个**线性模型**：

**`最终值 = K * (当前温度 - 基准温度) + 基准值`**

这个模型同时应用于 VCOM 和 Gamma 的补偿：
*   `Y_vcom = K_vcom * (Temp - T_ref) + B_vcom`
*   `Y_gamma = K_gamma * (Temp - T_ref) + B_gamma`

其中：
*   `Y` 是补偿后的最终值。
*   `K` 是补偿系数（斜率）。
*   `Temp` 是当前实时温度。
*   `T_ref` 是基准温度（如25°C）。
*   `B` 是在基准温度 `T_ref` 下的设定值（特征值）。

现在，我们把这个模型和 Verilog 接口、数据流图对应起来。

---

### 信号功能

| Verilog 信号名 | 信号类型 | 精确功能推测（结合新图） | 对应数据流图/原理图中的变量 |
| :--- | :--- | :--- | :--- |
| **输入** | | | |
| `vcom` | `input [7:0]` | **VCOM基准值**。在基准温度下的VCOM设定值。 | `b_vcom` (原理图), `vcom(s, 8, 0)` (数据流图) |
| `vcom_tcomp_coeff` | `input [7:0]` | **VCOM温度补偿系数（斜率K）**。格式为(u,1,7)，表示1位整数，7位小数。 | `K_vcom` (原理图), `vcom_tcomp_coeff_eff(u,1,7)` (数据流图) |
| `gamma_tcomp_coeff` | `input [7:0]` | **Gamma温度补偿系数（斜率K）**。格式与VCOM系数相同。 | `K_gm` (原理图), `gamma_tcomp_coeff_eff(u,1,7)` (数据流图) |
| `temp` | `input [7:0]` | **当前实时温度值**。 | `Temp` (原理图), 用于计算 `delta_temp_eff` |
| `temp_vcom` | `input [7:0]` | **基准温度**。 | `T_ref` (特征温度T, 原理图), 用于计算 `delta_temp_eff` |
| `vcom_tcomp_en` | `input` | **补偿功能总开关**。从数据流图看，它控制一个MUX，决定输出是`补偿后的VCOM`还是`原始VCOM`。 | `自动温补功能设定/开关` (原理图), `MUX`的选择信号 |
| `vcom_tcomp_th` | `input [7:0]` | **补偿触发阈值**。可以是温度变化阈值（如1°C, 3°C, 5°C）或定时器周期。 | `阈值触发` (原理图) |
| `temp_vld` / `timer_ls_vld` | `input` | **触发信号**。表明温度数据更新或定时器到期，可以进行一次新的补偿计算。 | `阈值触发` (原理图) |
| **输出** | | | |
| `vcom_tcomp` | `output [7:0]` | **最终补偿后的VCOM值**。这是模块计算的完整结果。 | `Y_vcom` (原理图), `vcom_tcomp_out(s, 9, 0)` (数据流图) |
| `gamma_tcomp` | `output [13:0]` | **计算出的Gamma补偿 *增量***。**注意**：它不是最终的Gamma值，而是`K_gm * (Temp - T_ref)`这部分。 | `K_gm * X` (原理图), `gamma_tcomp(s, 9, 5)` (数据流图) |

---

### 数据流图解析

#### 1. VCOM 补偿路径（上路）

这条路径完整实现了 `Y_vcom = K_vcom * (Temp - T_ref) + B_vcom` 的计算。

1.  **计算温差 `delta_temp_eff`**: 模块内部首先用输入的 `temp` 减去 `temp_vcom`，得到温差 `delta_temp_eff(s, 8, 1)`。这是一个带符号的定点数，可以表示正负温差（如-10.5°C）。
2.  **乘法**: 将温差 `delta_temp_eff` 与 VCOM补偿系数 `vcom_tcomp_coeff_eff` 相乘。`vcoef_mult_delta_tmp(s, 9, 8)` 就是 `K_vcom * (Temp - T_ref)` 的结果，这是一个高精度中间值。
3.  **Round（四舍五入）**: 将乘法结果的小数部分进行四舍五入，得到一个整数的补偿**增量** `ΔVCOM`。`vcoef_mult_rnd(s, 9, 0)` 就是这个增量。
4.  **加法**: 将计算出的增量 `ΔVCOM` 与输入的基准值 `vcom` 相加，得到补偿后的临时VCOM值 `vcom_tcomp_tmp(s, 10, 0)`。
5.  **MUX（选择器）**: 由 `vcom_tcomp_en` 控制。
    *   若 `en=1`，选择加法器的结果，使能补偿。
    *   若 `en=0`，选择原始的 `vcom` 值（图中未画出，但这是标准做法，或者选择0，然后由软件处理），禁用补偿。
6.  **SAT（饱和运算）**: `vcom_tcomp_unsigned(u, 9, 0)` 确保输出值不会超出有效范围（例如，防止VCOM值变成负数或超过最大值255）。
7.  **D（寄存器）**: `vcom_tcomp_out` 将最终结果锁存输出，这就是 `vcom_tcomp` 信号。

#### 2. Gamma 补偿路径（下路）

这条路径只计算了补偿公式中的**增量部分** `K_gm * (Temp - T_ref)`。

1.  **计算温差 `delta_temp_eff`**: 与VCOM路径共享同一个温差输入。
2.  **乘法**: 将温差 `delta_temp_eff` 与 Gamma补偿系数 `gamma_tcomp_coeff_eff` 相乘，得到 `gcoef_mult_delta_tmp(s, 9, 8)`。
3.  **Round（四舍五入）**: **注意这里的关键区别！** 它将结果舍入到 `(s, 9, 5)` 格式，即保留了5位小数。这说明Gamma补偿需要更高的精度。
4.  **D（寄存器）**: `gamma_tcomp(s, 9, 5)` 直接将这个高精度的**补偿增量**锁存输出。

### 关键结论

1.  **VCOM输出是绝对值**: `vcom_tcomp` 输出的是一个可以直接写入寄存器的、经过完整计算的**最终VCOM值**。
2.  **Gamma输出是增量值**: `gamma_tcomp` 输出的是一个高精度的**补偿增量（ΔGamma）**。这个值还需要在另一个模块（可能是Gamma LUT模块）中与Gamma的基准值相加，才能得到最终的Gamma设定。
3.  **模块分工明确**: 这个 `VCOM_TEMP_COMP` 模块是一个通用的线性补偿计算引擎。它为VCOM提供了“一站式”的补偿方案，同时为Gamma电路提供了核心的“补偿增量”计算，体现了良好的模块化设计思想。
4.  **Verilog接口与设计意图完全吻合**:
    *   `vcom_tcomp_coeff` 和 `gamma_tcomp_coeff` 就是线性公式中的斜率 `K`。
    *   `vcom` 和 `temp_vcom` 定义了补偿的基准点 `(T_ref, B_vcom)`。
    *   `vcom_tcomp` 和 `gamma_tcomp` 就是计算结果，只是一个输出了最终值，一个输出了中间增量。